# 金舟国际物流 - 移动端应用

基于uni-app + Vue3开发的金舟国际物流管理系统移动端应用。

## 项目特性

- 🚀 基于uni-app框架，支持多端发布（H5、小程序、App）
- 💎 使用Vue3 Composition API
- 🎨 参考网页版设计，保持UI风格一致
- 🔐 完整的用户登录认证系统
- 📱 响应式设计，适配各种屏幕尺寸
- 🛠 模块化架构，易于维护和扩展

## 项目结构

```
uni-app-Vue3/
├── pages/                  # 页面文件
│   ├── login/             # 登录页面
│   │   └── login.vue
│   └── index/             # 首页
│       └── index.vue
├── utils/                  # 工具类
│   ├── api.js             # API请求工具
│   └── user.js            # 用户状态管理
├── static/                # 静态资源
│   └── logo.jpg           # 应用Logo
├── App.vue                # 应用入口组件
├── main.js                # 应用入口文件
├── pages.json             # 页面路由配置
├── manifest.json          # 应用配置文件
└── uni.scss              # 全局样式
```

## 功能特性

### 已实现功能

1. **用户登录**
   - 账号密码登录
   - 微信登录（UI已完成，功能待对接）
   - 登录状态管理
   - 自动跳转和权限验证

2. **首页功能**
   - 用户信息显示
   - 功能菜单导航
   - 快捷操作入口
   - 用户登录/退出

3. **工具类**
   - 统一的API请求封装
   - 用户状态管理
   - 本地存储管理

### 待开发功能

- 订单管理
- 物流跟踪
- 仓库管理
- 财务管理
- 用户注册
- 忘记密码
- 个人中心

## 开发环境要求

- Node.js 14+
- HBuilderX 或 VS Code + uni-app插件
- 微信开发者工具（小程序开发）

## 安装和运行

### 1. 克隆项目
```bash
# 项目已在当前目录
cd uni-app-Vue3
```

### 2. 安装依赖
```bash
npm install
```

### 3. 运行项目

#### H5端开发
```bash
npm run dev:h5
```

#### 微信小程序开发
```bash
npm run dev:mp-weixin
```

#### App端开发
```bash
npm run dev:app-plus
```

### 4. 构建发布
```bash
# H5端构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# App端构建
npm run build:app-plus
```

## 配置说明

### API配置
在 `utils/api.js` 中修改API服务器地址：
```javascript
const API_CONFIG = {
    baseUrl: 'http://localhost:8080',  // 修改为实际的后端服务地址
    timeout: 10000
};
```

### 应用配置
在 `manifest.json` 中配置应用信息：
- 应用名称
- 应用ID
- 版本信息
- 平台特定配置

## 页面说明

### 登录页面 (`/pages/login/login.vue`)
- 支持账号密码登录和微信登录切换
- 表单验证和错误提示
- 登录状态管理
- 自动跳转功能

### 首页 (`/pages/index/index.vue`)
- 显示用户信息和欢迎信息
- 功能菜单网格布局
- 快捷操作按钮
- 登录/退出功能

## 开发注意事项

1. **样式单位**：使用rpx作为响应式单位
2. **API调用**：统一使用 `utils/api.js` 中的方法
3. **用户状态**：使用 `utils/user.js` 管理用户登录状态
4. **页面跳转**：使用uni-app的路由API
5. **平台兼容**：注意不同平台的API差异

## 与后端对接

确保后端服务提供以下API接口：

- `POST /api/login` - 用户登录
- `POST /api/register` - 用户注册
- `POST /api/forgot-password` - 忘记密码
- `GET /api/user/{id}` - 获取用户信息
- `POST /api/validate-token` - 验证token

## 部署说明

### H5部署
构建后将 `dist/build/h5` 目录部署到Web服务器

### 小程序发布
1. 构建小程序版本
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 上传代码并提交审核

### App打包
1. 在HBuilderX中打开项目
2. 选择发行 -> 原生App-云打包
3. 配置签名和证书
4. 提交打包

## 技术支持

如有问题，请联系开发团队或查看uni-app官方文档：
- [uni-app官方文档](https://uniapp.dcloud.io/)
- [Vue3官方文档](https://v3.vuejs.org/)
