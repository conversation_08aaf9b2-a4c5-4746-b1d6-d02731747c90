<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端预览 - 金舟国际物流</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .device-frames {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .device-frame {
            background: #333;
            border-radius: 25px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .phone-portrait {
            width: 320px;
            height: 600px;
        }
        
        .phone-landscape {
            width: 600px;
            height: 320px;
        }
        
        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 15px;
            background: white;
        }
        
        .device-label {
            text-align: center;
            color: white;
            margin-top: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .instructions h3 {
            color: #0c4da2;
            margin-bottom: 15px;
        }
        
        .instructions ul {
            line-height: 1.8;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #0c4da2;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .test-btn:hover {
            background-color: #083778;
        }
        
        .responsive-info {
            background: #e8f4fd;
            border-left: 4px solid #0c4da2;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .responsive-info h4 {
            color: #0c4da2;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>📱 金舟国际物流 - 手机端预览</h1>
            <p>查看网站在不同手机屏幕尺寸下的显示效果</p>
            
            <div class="test-buttons">
                <a href="main.html" class="test-btn" target="_blank">打开主页面</a>
                <a href="index.html" class="test-btn" target="_blank">打开首页</a>
            </div>
        </div>
        
        <div class="device-frames">
            <div class="device-frame phone-portrait">
                <iframe src="main.html" class="device-screen"></iframe>
                <div class="device-label">手机竖屏 (320×600)</div>
            </div>
            
            <div class="device-frame phone-landscape">
                <iframe src="main.html" class="device-screen"></iframe>
                <div class="device-label">手机横屏 (600×320)</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎯 手机端优化特性</h3>
            
            <div class="responsive-info">
                <h4>竖屏模式 (Portrait)</h4>
                <ul>
                    <li>头部导航垂直排列，节省空间</li>
                    <li>卡片单列显示，便于滚动浏览</li>
                    <li>字体和图标适当缩小</li>
                    <li>社交图标在小屏幕上隐藏</li>
                </ul>
            </div>
            
            <div class="responsive-info">
                <h4>横屏模式 (Landscape)</h4>
                <ul>
                    <li>头部导航水平排列，充分利用宽度</li>
                    <li>卡片双列显示，一屏显示更多内容</li>
                    <li>推荐按钮居中显示</li>
                    <li>整体布局更紧凑</li>
                </ul>
            </div>
            
            <h3>📋 测试方法</h3>
            <ul>
                <li><strong>手机测试：</strong>用手机浏览器直接访问网站</li>
                <li><strong>浏览器测试：</strong>使用Chrome开发者工具的设备模拟功能</li>
                <li><strong>响应式测试：</strong>调整浏览器窗口大小观察布局变化</li>
                <li><strong>方向测试：</strong>旋转手机或在开发者工具中切换方向</li>
            </ul>
            
            <h3>🔧 技术实现</h3>
            <ul>
                <li><strong>断点设置：</strong>768px以下为移动端，480px以下为小屏手机</li>
                <li><strong>布局策略：</strong>使用CSS Flexbox实现灵活布局</li>
                <li><strong>方向检测：</strong>使用CSS媒体查询 <code>orientation: landscape</code></li>
                <li><strong>兼容性：</strong>保持电脑端完美布局不变</li>
            </ul>
            
            <h3>✨ 优化效果</h3>
            <ul>
                <li>✅ 解决手机端显示不全的问题</li>
                <li>✅ 提供更好的移动端用户体验</li>
                <li>✅ 横屏时充分利用屏幕空间</li>
                <li>✅ 保持电脑端原有的完美布局</li>
                <li>✅ 支持各种屏幕尺寸和方向</li>
            </ul>
        </div>
    </div>
</body>
</html>
