# 金舟国际物流App - 快速开始指南

## 项目概述

这是基于uni-app + Vue3开发的金舟国际物流管理系统移动端应用，参考了网页版login.html的设计风格。

## 主要功能

✅ **已完成功能：**
- 用户登录页面（账号密码登录 + 微信登录UI）
- 首页展示（用户信息、功能菜单、快捷操作）
- 用户状态管理（登录/退出、本地存储）
- API请求封装
- 功能测试页面

🚧 **待开发功能：**
- 订单管理
- 物流跟踪
- 仓库管理
- 财务管理
- 用户注册
- 忘记密码

## 快速启动

### 1. 使用HBuilderX（推荐）

1. 打开HBuilderX
2. 文件 -> 导入 -> 从本地目录导入
3. 选择 `uni-app-Vue3` 文件夹
4. 运行 -> 运行到浏览器 -> Chrome（H5端测试）
5. 或运行 -> 运行到小程序模拟器 -> 微信开发者工具

### 2. 使用命令行

```bash
# 进入项目目录
cd uni-app-Vue3

# 安装依赖（如果需要）
npm install

# 运行H5版本
npm run dev:h5

# 运行微信小程序版本
npm run dev:mp-weixin
```

## 页面结构

```
📱 App
├── 🔐 登录页面 (/pages/login/login)
│   ├── 账号密码登录
│   ├── 微信登录（UI完成）
│   ├── 表单验证
│   └── 登录状态管理
├── 🏠 首页 (/pages/index/index)
│   ├── 用户信息展示
│   ├── 功能菜单（4个主要功能）
│   ├── 快捷操作
│   └── 登录/退出功能
└── 🧪 测试页面 (/pages/test/test)
    ├── 用户状态测试
    ├── API连接测试
    ├── 本地存储测试
    └── 系统信息显示
```

## 测试流程

### 1. 启动应用
- 应用启动后会显示登录页面
- 可以看到金舟国际物流的Logo和登录表单

### 2. 测试登录功能
- 输入测试账号和密码
- 点击登录按钮
- 如果后端服务运行，会调用登录API
- 如果后端未运行，会显示连接失败提示

### 3. 查看首页
- 登录成功后跳转到首页
- 显示用户信息和欢迎消息
- 可以看到4个功能菜单和快捷操作按钮

### 4. 功能测试
- 点击首页的"功能测试"按钮
- 进入测试页面查看各项功能状态
- 测试API连接、本地存储等功能

## 配置说明

### API服务器配置
在 `utils/api.js` 中修改：
```javascript
const API_CONFIG = {
    baseUrl: 'http://localhost:8080',  // 修改为实际后端地址
    timeout: 10000
};
```

### 应用信息配置
在 `manifest.json` 中可以修改：
- 应用名称
- 应用图标
- 启动页面
- 权限配置

## 开发注意事项

1. **样式适配**：使用rpx单位确保多端适配
2. **API调用**：统一使用 `utils/api.js` 中的方法
3. **用户管理**：使用 `utils/user.js` 管理用户状态
4. **页面跳转**：使用uni-app的路由API
5. **调试工具**：使用测试页面进行功能验证

## 与后端对接

确保后端提供以下接口：

```
POST /api/login
{
  "username": "用户名或邮箱",
  "password": "密码"
}

响应格式：
{
  "success": true,
  "user": {
    "id": "用户ID",
    "username": "用户名",
    "email": "邮箱",
    "nickname": "昵称",
    "avatar": "头像URL",
    "roles": ["角色列表"],
    "permissions": ["权限列表"]
  }
}
```

## 常见问题

### Q: 登录时提示"服务器连接失败"
A: 检查后端服务是否启动，API地址是否正确

### Q: 页面样式显示异常
A: 确保使用rpx单位，检查CSS语法

### Q: 小程序运行报错
A: 检查微信开发者工具是否正确配置，appid是否设置

### Q: 如何添加新页面
A: 1. 在pages目录创建页面文件 2. 在pages.json中添加路由配置

## 下一步开发计划

1. 完善用户注册和忘记密码功能
2. 开发订单管理模块
3. 实现物流跟踪功能
4. 添加仓库管理界面
5. 集成财务管理功能
6. 优化UI/UX体验
7. 添加更多测试用例

## 技术支持

如有问题，请查看：
- [uni-app官方文档](https://uniapp.dcloud.io/)
- [Vue3官方文档](https://v3.vuejs.org/)
- 项目README.md文件
