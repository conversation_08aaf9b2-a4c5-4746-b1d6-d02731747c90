<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推荐商品加入购物车测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .product-card {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #6b7280;
        }

        .product-info {
            padding: 15px;
        }

        .product-name {
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .current-price {
            color: #ef4444;
            font-weight: 700;
            font-size: 18px;
        }

        .original-price {
            color: #9ca3af;
            text-decoration: line-through;
            font-size: 14px;
        }

        .add-to-cart-btn {
            width: 100%;
            padding: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .add-to-cart-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .login-section {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .login-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }

        .status-message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }

        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推荐商品加入购物车功能测试</h1>
        
        <div class="login-section">
            <p>当前状态: <span id="loginStatus">未登录</span></p>
            <button class="login-btn" onclick="simulateLogin()">模拟登录</button>
            <button class="logout-btn" onclick="simulateLogout()">退出登录</button>
        </div>

        <div id="statusMessage"></div>

        <div class="products-grid" id="productsGrid">
            <!-- 商品将在这里显示 -->
        </div>
    </div>

    <script>
        // 模拟商品数据
        const mockProducts = [
            {
                id: '1',
                name: '中东海运超大件敏货',
                price: 100.00,
                originalPrice: 130.00,
                mainImage: '',
                description: '专业海运服务',
                stock: 50,
                emoji: '🚢'
            },
            {
                id: '2',
                name: '东南亚空运敏货',
                price: 1.00,
                originalPrice: 1.30,
                mainImage: '',
                description: '快速空运服务',
                stock: 30,
                emoji: '✈️'
            },
            {
                id: '3',
                name: '欧洲海运超大件',
                price: 2.00,
                originalPrice: 2.50,
                mainImage: '',
                description: '欧洲专线服务',
                stock: 20,
                emoji: '🌍'
            }
        ];

        // 模拟 ProductManager
        class MockProductManager {
            constructor() {
                this.allProducts = mockProducts;
            }

            getProductById(productId) {
                return this.allProducts.find(product => product.id === productId);
            }
        }

        // 初始化模拟的 ProductManager
        window.productManager = new MockProductManager();

        // 渲染商品
        function renderProducts() {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = mockProducts.map(product => `
                <div class="product-card">
                    <div class="product-image">${product.emoji}</div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-price">
                            <span class="current-price">¥${product.price.toFixed(2)}</span>
                            <span class="original-price">¥${product.originalPrice.toFixed(2)}</span>
                        </div>
                        <button class="add-to-cart-btn" onclick="event.stopPropagation(); addToCart('${product.id}')">
                            加入购物车
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 模拟登录
        function simulateLogin() {
            const userData = {
                isLoggedIn: true,
                username: 'testuser',
                email: '<EMAIL>'
            };
            sessionStorage.setItem('loggedInUser', JSON.stringify(userData));
            updateLoginStatus();
            showMessage('模拟登录成功！', 'success');
        }

        // 模拟退出登录
        function simulateLogout() {
            sessionStorage.removeItem('loggedInUser');
            updateLoginStatus();
            showMessage('已退出登录', 'info');
        }

        // 更新登录状态显示
        function updateLoginStatus() {
            const statusElement = document.getElementById('loginStatus');
            try {
                const userData = sessionStorage.getItem('loggedInUser');
                if (userData) {
                    const user = JSON.parse(userData);
                    if (user.isLoggedIn) {
                        statusElement.textContent = `已登录 (${user.username})`;
                        statusElement.style.color = '#059669';
                        return;
                    }
                }
            } catch (error) {
                console.warn('解析用户数据失败:', error);
            }
            statusElement.textContent = '未登录';
            statusElement.style.color = '#dc2626';
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('statusMessage');
            messageDiv.innerHTML = `<div class="status-message ${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }

        // 修复后的 addToCart 函数（与推荐页面相同的逻辑）
        async function addToCart(productId) {
            console.log('添加商品到购物车:', productId);

            // 检查用户是否已登录
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data:', error);
            }

            if (!loggedInUser || !loggedInUser.isLoggedIn || !loggedInUser.username) {
                showMessage('请先登录后再添加商品到购物车', 'error');
                return;
            }

            try {
                // 获取商品数据 - 先尝试从本地获取，如果没有则从服务器获取
                let product = window.productManager ? window.productManager.getProductById(productId) : null;

                // 如果本地没有商品数据，从服务器获取（这里模拟成功）
                if (!product) {
                    console.log('本地没有商品数据，模拟从服务器获取...');
                    // 在实际应用中，这里会发送 fetch 请求
                    // 这里我们模拟找到了商品
                    product = mockProducts.find(p => p.id === productId);
                }

                if (!product) {
                    showMessage('商品信息获取失败', 'error');
                    return;
                }

                // 模拟调用购物车API（实际应用中会发送到服务器）
                console.log('模拟发送到服务器的数据:', {
                    username: loggedInUser.username,
                    productId: productId,
                    productData: {
                        name: product.name,
                        price: product.price,
                        mainImage: product.mainImage || '',
                        description: product.description || '',
                        stock: product.stock || 0
                    }
                });

                // 模拟成功响应
                showMessage(`商品"${product.name}"已添加到购物车！`, 'success');

            } catch (error) {
                console.error('添加到购物车失败:', error);
                showMessage('添加到购物车失败，请稍后重试', 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderProducts();
            updateLoginStatus();
        });
    </script>
</body>
</html>
