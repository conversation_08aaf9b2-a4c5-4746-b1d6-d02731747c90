<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金舟国际物流</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }
        .container {
            text-align: center;
        }
        .logo-img {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
        }
        h1 {
            color: #0c4da2;
            font-size: 48px;
            margin-bottom: 10px;
        }
        .gold {
            color: #D4AF37;
        }
        p {
            color: #666;
            font-size: 24px;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: #0c4da2;
            color: #fff;
            text-decoration: none;
            font-size: 18px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #083778;
        }
        .btn-text {
            display: block;
        }
        .btn-subtext {
            display: block;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
        <h1><span class="gold">金舟</span>国际物流</h1>
        <p>Jin Zhou International Logistics</p>
        <a href="main.html" class="btn">
            <span class="btn-text">进入官网</span>
            <span class="btn-subtext">Enter Website</span>
        </a>
    </div>
    
    <script>
        // 检查登录状态，为了在首页也能保持登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 安全地检查登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                // 清除损坏的数据
                sessionStorage.removeItem('loggedInUser');
            }

            // 点击进入按钮时的处理
            const enterButton = document.querySelector('.btn');
            if (enterButton) {
                enterButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'main.html';
                });
            }
        });
    </script>
</body>
</html> 