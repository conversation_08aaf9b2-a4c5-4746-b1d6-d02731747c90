<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #0c4da2;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-step {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #0c4da2;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0a3d82;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #0c4da2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav-links a:hover {
            background-color: #0a3d82;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">购物车数据同步测试</h1>
        
        <div class="nav-links">
            <a href="my-products.html" target="_blank">打开购物车页面</a>
            <a href="admin-dashboard.html" target="_blank">打开商品管理页面</a>
        </div>

        <div class="test-step">
            <h3>测试步骤：</h3>
            <ol>
                <li>确保已登录用户账户</li>
                <li>在购物车中添加一些商品</li>
                <li>打开商品管理页面，修改商品的名称、图片或价格</li>
                <li>返回购物车页面，检查商品信息是否自动更新</li>
                <li>或者点击购物车页面的刷新按钮手动同步</li>
            </ol>
        </div>

        <div class="test-step">
            <h3>测试功能：</h3>
            <button onclick="testCartSync()">测试购物车同步</button>
            <button onclick="simulateProductUpdate()">模拟商品更新</button>
            <button onclick="checkCartData()">检查购物车数据</button>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        // 测试购物车同步功能
        async function testCartSync() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result info">正在测试购物车同步功能...</div>';

            try {
                // 检查是否有登录用户
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (!userDataStr) {
                    resultsDiv.innerHTML = '<div class="test-result error">错误：未找到登录用户信息</div>';
                    return;
                }

                const userData = JSON.parse(userDataStr);
                resultsDiv.innerHTML += '<div class="test-result success">✓ 找到登录用户：' + userData.username + '</div>';

                // 获取购物车数据
                const cartResponse = await fetch(`/api/cart/${encodeURIComponent(userData.username)}`);
                const cartData = await cartResponse.json();

                if (cartData.success && cartData.cart && cartData.cart.length > 0) {
                    resultsDiv.innerHTML += '<div class="test-result success">✓ 购物车中有 ' + cartData.cart.length + ' 个商品</div>';

                    // 测试获取最新商品信息
                    const firstItem = cartData.cart[0];
                    const productResponse = await fetch(`/api/products/${firstItem.productId}`);
                    const productData = await productResponse.json();

                    if (productData.success) {
                        resultsDiv.innerHTML += '<div class="test-result success">✓ 成功获取商品最新信息：' + productData.product.name + '</div>';

                        // 获取商品的最新图片
                        let latestImage = '';
                        if (productData.product.images && productData.product.images.length > 0) {
                            latestImage = productData.product.images[0].url;
                        } else if (productData.product.mainImage) {
                            latestImage = productData.product.mainImage;
                        }

                        // 比较购物车中的信息和最新商品信息
                        const differences = [];
                        if (firstItem.name !== productData.product.name) {
                            differences.push(`名称: "${firstItem.name}" → "${productData.product.name}"`);
                        }
                        if (firstItem.price !== productData.product.price) {
                            differences.push(`价格: ${firstItem.price} → ${productData.product.price}`);
                        }
                        if (firstItem.image !== latestImage) {
                            differences.push(`图片: "${firstItem.image}" → "${latestImage}"`);
                        }

                        if (differences.length > 0) {
                            resultsDiv.innerHTML += '<div class="test-result info">发现差异需要同步：<br>' + differences.join('<br>') + '</div>';
                        } else {
                            resultsDiv.innerHTML += '<div class="test-result success">✓ 购物车信息与商品信息一致</div>';
                        }

                        // 显示图片信息详情
                        resultsDiv.innerHTML += '<div class="test-result info">' +
                            '<strong>图片同步详情：</strong><br>' +
                            '购物车中的图片: ' + (firstItem.image || '无') + '<br>' +
                            '商品最新图片: ' + (latestImage || '无') + '<br>' +
                            '商品images数组: ' + JSON.stringify(productData.product.images || []) +
                            '</div>';
                    } else {
                        resultsDiv.innerHTML += '<div class="test-result error">✗ 获取商品信息失败</div>';
                    }
                } else {
                    resultsDiv.innerHTML += '<div class="test-result info">购物车为空，请先添加商品进行测试</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="test-result error">测试失败：' + error.message + '</div>';
            }
        }

        // 模拟商品更新
        async function simulateProductUpdate() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result info">模拟商品更新功能...</div>';
            
            // 这里可以添加模拟商品更新的代码
            resultsDiv.innerHTML += '<div class="test-result info">请手动在商品管理页面修改商品信息来测试同步功能</div>';
        }

        // 检查购物车数据
        async function checkCartData() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result info">检查购物车数据...</div>';

            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (!userDataStr) {
                    resultsDiv.innerHTML = '<div class="test-result error">错误：未找到登录用户信息</div>';
                    return;
                }

                const userData = JSON.parse(userDataStr);
                const cartResponse = await fetch(`/api/cart/${encodeURIComponent(userData.username)}`);
                const cartData = await cartResponse.json();

                if (cartData.success) {
                    resultsDiv.innerHTML += '<div class="test-result success">购物车数据：</div>';
                    resultsDiv.innerHTML += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">' + 
                        JSON.stringify(cartData.cart, null, 2) + '</pre>';
                } else {
                    resultsDiv.innerHTML += '<div class="test-result error">获取购物车数据失败</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="test-result error">检查失败：' + error.message + '</div>';
            }
        }

        // 页面加载时显示说明
        window.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `
                <div class="test-result info">
                    <h4>购物车同步功能说明：</h4>
                    <ul>
                        <li>✅ 购物车渲染时自动获取最新商品信息（名称、图片、价格、库存）</li>
                        <li>✅ 页面获得焦点时自动刷新购物车</li>
                        <li>✅ 页面可见性变化时自动刷新购物车</li>
                        <li>✅ 手动刷新按钮可立即同步最新信息</li>
                        <li>✅ 同步后的信息会保存到服务器</li>
                    </ul>
                </div>
            `;
        });
    </script>
</body>
</html>
