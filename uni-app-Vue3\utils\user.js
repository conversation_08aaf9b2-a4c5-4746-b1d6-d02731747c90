// 用户状态管理工具类

/**
 * 获取当前登录用户信息
 * @returns {object|null} 用户信息对象或null
 */
export function getCurrentUser() {
	try {
		const userData = uni.getStorageSync('loggedInUser');
		if (userData && userData.isLoggedIn) {
			return userData;
		}
		return null;
	} catch (error) {
		console.error('获取用户信息失败:', error);
		return null;
	}
}

/**
 * 保存用户登录信息
 * @param {object} userData - 用户数据
 */
export function saveUserInfo(userData) {
	try {
		const userInfo = {
			...userData,
			isLoggedIn: true,
			loginTime: new Date().toISOString()
		};
		uni.setStorageSync('loggedInUser', userInfo);
		return true;
	} catch (error) {
		console.error('保存用户信息失败:', error);
		return false;
	}
}

/**
 * 清除用户登录信息（退出登录）
 */
export function clearUserInfo() {
	try {
		uni.removeStorageSync('loggedInUser');
		return true;
	} catch (error) {
		console.error('清除用户信息失败:', error);
		return false;
	}
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
	const user = getCurrentUser();
	return user !== null && user.isLoggedIn === true;
}

/**
 * 更新用户信息
 * @param {object} newData - 新的用户数据
 */
export function updateUserInfo(newData) {
	try {
		const currentUser = getCurrentUser();
		if (currentUser) {
			const updatedUser = {
				...currentUser,
				...newData,
				updateTime: new Date().toISOString()
			};
			uni.setStorageSync('loggedInUser', updatedUser);
			return true;
		}
		return false;
	} catch (error) {
		console.error('更新用户信息失败:', error);
		return false;
	}
}

/**
 * 检查登录状态是否过期
 * @param {number} expireHours - 过期时间（小时），默认24小时
 * @returns {boolean} 是否过期
 */
export function isLoginExpired(expireHours = 24) {
	const user = getCurrentUser();
	if (!user || !user.loginTime) {
		return true;
	}
	
	const loginTime = new Date(user.loginTime);
	const now = new Date();
	const diffHours = (now - loginTime) / (1000 * 60 * 60);
	
	return diffHours > expireHours;
}

/**
 * 获取用户头像URL
 * @param {object} user - 用户对象
 * @returns {string} 头像URL
 */
export function getUserAvatar(user) {
	if (user && user.avatar) {
		return user.avatar;
	}
	// 返回默认头像
	return '/static/default-avatar.png';
}

/**
 * 格式化用户显示名称
 * @param {object} user - 用户对象
 * @returns {string} 显示名称
 */
export function getUserDisplayName(user) {
	if (!user) return '未知用户';
	
	if (user.nickname) return user.nickname;
	if (user.username) return user.username;
	if (user.email) return user.email.split('@')[0];
	
	return '用户';
}

/**
 * 验证用户权限
 * @param {string} permission - 权限名称
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission) {
	const user = getCurrentUser();
	if (!user || !user.permissions) {
		return false;
	}
	
	return user.permissions.includes(permission);
}

/**
 * 检查用户角色
 * @param {string} role - 角色名称
 * @returns {boolean} 是否具有该角色
 */
export function hasRole(role) {
	const user = getCurrentUser();
	if (!user || !user.roles) {
		return false;
	}
	
	return user.roles.includes(role);
}

/**
 * 用户登录状态监听器
 */
export const userStateListeners = [];

/**
 * 添加用户状态变化监听器
 * @param {function} callback - 回调函数
 */
export function addUserStateListener(callback) {
	if (typeof callback === 'function') {
		userStateListeners.push(callback);
	}
}

/**
 * 移除用户状态变化监听器
 * @param {function} callback - 回调函数
 */
export function removeUserStateListener(callback) {
	const index = userStateListeners.indexOf(callback);
	if (index > -1) {
		userStateListeners.splice(index, 1);
	}
}

/**
 * 触发用户状态变化事件
 * @param {string} event - 事件类型 ('login', 'logout', 'update')
 * @param {object} data - 事件数据
 */
export function triggerUserStateChange(event, data) {
	userStateListeners.forEach(callback => {
		try {
			callback(event, data);
		} catch (error) {
			console.error('用户状态监听器执行失败:', error);
		}
	});
}
