<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结算 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 头部样式 */
        .header {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 50px;
            height: 50px;
            margin-right: 15px;
        }
        
        .logo-text h1 {
            color: #0c4da2;
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .logo-text .gold {
            color: #D4AF37;
        }
        
        .logo-text p {
            color: #666;
            font-size: 12px;
        }
        
        .back-btn {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.3s;
        }
        
        .back-btn:hover {
            background-color: #5a6268;
        }
        
        /* 主要内容区域 */
        .payment-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }
        
        /* 订单摘要 */
        .order-summary {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .section-title {
            color: #0c4da2;
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            margin-right: 15px;
            border: 1px solid #e5e7eb;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .item-price {
            color: #0c4da2;
            font-weight: bold;
        }
        
        .item-quantity {
            color: #666;
            font-size: 14px;
        }
        
        /* 支付区域 */
        .payment-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        /* 安全提示 */
        .security-notice {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 1px solid #28a745;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .security-notice i {
            color: #28a745;
            font-size: 18px;
        }
        
        .security-text {
            color: #155724;
            font-size: 14px;
        }
        
        /* 总金额显示 */
        .total-amount {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            color: #8b6914;
        }
        
        .total-label {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .total-price {
            font-size: 28px;
            font-weight: bold;
        }
        
        /* 支付方式选择 */
        .payment-methods {
            margin-bottom: 20px;
        }
        
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .payment-method:hover {
            border-color: #0c4da2;
            background-color: #f8f9ff;
        }
        
        .payment-method.selected {
            border-color: #0c4da2;
            background-color: #f8f9ff;
        }
        
        .payment-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
        }
        
        .wechat-icon {
            background-color: #07c160;
            color: white;
        }
        
        .alipay-icon {
            background-color: #1677ff;
            color: white;
        }
        
        .paypal-icon {
            background-color: #0070ba;
            color: white;
        }
        
        .payment-info {
            flex: 1;
        }
        
        .payment-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .payment-desc {
            color: #666;
            font-size: 14px;
        }
        
        /* 确认支付按钮 */
        .confirm-payment-btn {
            background: linear-gradient(135deg, #0c4da2 0%, #083778 100%);
            color: white;
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
        }
        
        .confirm-payment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }
        
        .confirm-payment-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .payment-container {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .total-price {
                font-size: 24px;
            }
            
            .payment-method {
                padding: 12px;
            }
            
            .payment-icon {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <div class="logo-text">
                    <h1><span class="gold">金舟</span>国际物流</h1>
                    <p>Jin Zhou International Logistics</p>
                </div>
            </div>
            <a href="javascript:history.back()" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                返回
            </a>
        </div>
        
        <!-- 主要内容 -->
        <div class="payment-container">
            <!-- 订单摘要 -->
            <div class="order-summary">
                <h2 class="section-title">
                    <i class="fas fa-list-alt"></i>
                    订单摘要
                </h2>
                <div id="order-items">
                    <!-- 订单项目将通过JavaScript动态加载 -->
                </div>
            </div>
            
            <!-- 支付区域 -->
            <div class="payment-section">
                <!-- 安全提示 -->
                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <div class="security-text">
                        <strong>安全支付保障</strong><br>
                        您的支付信息将通过SSL加密传输，确保交易安全
                    </div>
                </div>
                
                <!-- 总金额 -->
                <div class="total-amount">
                    <div class="total-label">支付总金额</div>
                    <div class="total-price" id="total-price">¥ 0.00</div>
                </div>
                
                <!-- 支付方式 -->
                <h3 class="section-title">
                    <i class="fas fa-credit-card"></i>
                    选择支付方式
                </h3>
                <div class="payment-methods">
                    <div class="payment-method" data-method="wechat">
                        <div class="payment-icon wechat-icon">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div class="payment-info">
                            <div class="payment-name">微信支付</div>
                            <div class="payment-desc">使用微信扫码支付，安全便捷</div>
                        </div>
                    </div>
                    
                    <div class="payment-method" data-method="alipay">
                        <div class="payment-icon alipay-icon">
                            <i class="fab fa-alipay"></i>
                        </div>
                        <div class="payment-info">
                            <div class="payment-name">支付宝支付</div>
                            <div class="payment-desc">支持支付宝账户余额、银行卡支付</div>
                        </div>
                    </div>
                    
                    <div class="payment-method" data-method="paypal">
                        <div class="payment-icon paypal-icon">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="payment-info">
                            <div class="payment-name">PayPal支付</div>
                            <div class="payment-desc">国际支付，支持多种货币</div>
                        </div>
                    </div>
                </div>
                
                <!-- 确认支付按钮 -->
                <button class="confirm-payment-btn" id="confirm-payment-btn" disabled>
                    <i class="fas fa-lock"></i>
                    确认支付
                </button>
            </div>
        </div>
    </div>

    <script>
        // 支付页面管理器
        class PaymentManager {
            constructor() {
                this.orderData = null;
                this.selectedPaymentMethod = null;
                this.currentUser = null;
                this.init();
            }

            init() {
                // 获取当前登录用户
                try {
                    const userDataStr = sessionStorage.getItem('loggedInUser');
                    if (userDataStr) {
                        this.currentUser = JSON.parse(userDataStr);
                    }
                } catch (error) {
                    console.warn('Failed to parse user data:', error);
                }

                // 检查用户登录状态
                if (!this.currentUser) {
                    alert('请先登录后再进行支付');
                    window.location.href = 'recommend.html';
                    return;
                }

                // 获取订单数据
                this.loadOrderData();

                // 绑定事件
                this.bindEvents();
            }

            // 加载订单数据
            loadOrderData() {
                // 从URL参数或sessionStorage获取订单数据
                const urlParams = new URLSearchParams(window.location.search);
                const orderType = urlParams.get('type'); // 'cart' 或 'single'
                const productId = urlParams.get('productId');

                if (orderType === 'single' && productId) {
                    // 单个商品购买
                    this.loadSingleProductOrder(productId);
                } else {
                    // 购物车结算
                    this.loadCartOrder();
                }
            }

            // 加载单个商品订单
            async loadSingleProductOrder(productId) {
                try {
                    // 从购物车中获取商品信息
                    const response = await fetch(`/api/cart/${encodeURIComponent(this.currentUser.username)}`);
                    const data = await response.json();

                    if (data.success && data.cart) {
                        const item = data.cart.find(item => item.productId === productId);
                        if (item) {
                            this.orderData = {
                                type: 'single',
                                items: [item],
                                totalAmount: item.price * item.quantity,
                                totalItems: item.quantity
                            };
                            this.renderOrder();
                        } else {
                            throw new Error('商品不存在');
                        }
                    } else {
                        throw new Error('获取商品信息失败');
                    }
                } catch (error) {
                    console.error('加载单个商品订单失败:', error);
                    alert('加载订单信息失败，请返回重试');
                    history.back();
                }
            }

            // 加载购物车订单
            async loadCartOrder() {
                try {
                    const response = await fetch(`/api/cart/${encodeURIComponent(this.currentUser.username)}`);
                    const data = await response.json();

                    if (data.success && data.cart && data.cart.length > 0) {
                        const totalAmount = data.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                        const totalItems = data.cart.reduce((sum, item) => sum + item.quantity, 0);

                        this.orderData = {
                            type: 'cart',
                            items: data.cart,
                            totalAmount: totalAmount,
                            totalItems: totalItems
                        };
                        this.renderOrder();
                    } else {
                        alert('购物车为空，无法进行支付');
                        window.location.href = 'my-products.html';
                    }
                } catch (error) {
                    console.error('加载购物车订单失败:', error);
                    alert('加载订单信息失败，请返回重试');
                    history.back();
                }
            }

            // 渲染订单信息
            renderOrder() {
                if (!this.orderData) return;

                // 渲染订单项目
                const orderItemsContainer = document.getElementById('order-items');
                const orderItemsHtml = this.orderData.items.map(item => `
                    <div class="order-item">
                        <img src="${item.image || 'https://via.placeholder.com/60x60/f0f0f0/999999?text=暂无图片'}"
                             alt="${item.name}" class="item-image"
                             onerror="this.src='https://via.placeholder.com/60x60/f0f0f0/999999?text=暂无图片'">
                        <div class="item-details">
                            <div class="item-name">${item.name}</div>
                            <div class="item-quantity">数量: ${item.quantity}</div>
                            <div class="item-price">¥ ${(item.price * item.quantity).toFixed(2)}</div>
                        </div>
                    </div>
                `).join('');

                orderItemsContainer.innerHTML = orderItemsHtml;

                // 更新总金额
                document.getElementById('total-price').textContent = `¥ ${this.orderData.totalAmount.toFixed(2)}`;
            }

            // 绑定事件
            bindEvents() {
                // 支付方式选择
                const paymentMethods = document.querySelectorAll('.payment-method');
                paymentMethods.forEach(method => {
                    method.addEventListener('click', () => {
                        // 移除其他选中状态
                        paymentMethods.forEach(m => m.classList.remove('selected'));
                        // 添加选中状态
                        method.classList.add('selected');
                        // 记录选中的支付方式
                        this.selectedPaymentMethod = method.dataset.method;
                        // 启用支付按钮
                        document.getElementById('confirm-payment-btn').disabled = false;
                    });
                });

                // 确认支付按钮
                document.getElementById('confirm-payment-btn').addEventListener('click', () => {
                    this.processPayment();
                });
            }

            // 处理支付
            processPayment() {
                if (!this.selectedPaymentMethod) {
                    alert('请选择支付方式');
                    return;
                }

                if (!this.orderData) {
                    alert('订单信息错误，请重新下单');
                    return;
                }

                // 显示支付处理中的状态
                const confirmBtn = document.getElementById('confirm-payment-btn');
                const originalText = confirmBtn.innerHTML;
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                confirmBtn.disabled = true;

                // 模拟支付处理
                setTimeout(() => {
                    this.showPaymentResult();
                }, 2000);
            }

            // 显示支付结果
            showPaymentResult() {
                const paymentMethodNames = {
                    'wechat': '微信支付',
                    'alipay': '支付宝支付',
                    'paypal': 'PayPal支付'
                };

                const methodName = paymentMethodNames[this.selectedPaymentMethod] || '未知支付方式';

                alert(`支付功能开发中...\n\n订单信息:\n商品数量: ${this.orderData.totalItems} 件\n支付金额: ¥${this.orderData.totalAmount.toFixed(2)}\n支付方式: ${methodName}\n\n此功能将在后续版本中实现真实的支付接口集成。`);

                // 恢复按钮状态
                const confirmBtn = document.getElementById('confirm-payment-btn');
                confirmBtn.innerHTML = '<i class="fas fa-lock"></i> 确认支付';
                confirmBtn.disabled = false;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            window.paymentManager = new PaymentManager();
        });
    </script>
</body>
</html>
