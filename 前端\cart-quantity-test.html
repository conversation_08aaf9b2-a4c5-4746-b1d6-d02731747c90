<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车数量编辑测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .cart-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 15px;
            background: #f9fafb;
        }

        .product-image {
            width: 60px;
            height: 60px;
            background: #e5e7eb;
            border-radius: 8px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .product-info {
            flex: 1;
        }

        .product-name {
            font-weight: 600;
            color: #111827;
            margin-bottom: 5px;
        }

        .product-price {
            color: #ef4444;
            font-weight: 600;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cart-quantity-btn {
            width: 28px;
            height: 28px;
            border: 1px solid #d1d5db;
            background: #ffffff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            transition: all 0.2s ease;
        }

        .cart-quantity-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .cart-quantity-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .cart-quantity-btn.plus:hover {
            background: #f0fdf4;
            color: #059669;
        }

        .quantity-display {
            min-width: 40px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: #111827;
            background: transparent;
            border: none;
            margin: 0 4px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
            position: relative;
        }

        .quantity-display:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .quantity-input-field {
            min-width: 40px;
            height: 28px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            border: 2px solid #3b82f6;
            border-radius: 4px;
            margin: 0 4px;
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .quantity-input-field:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1e40af;
        }

        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1e3a8a;
        }

        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>购物车数量编辑功能测试</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
                <li>点击数量数字可以直接编辑</li>
                <li>按 Enter 键确认修改</li>
                <li>按 Esc 键取消修改</li>
                <li>点击其他地方也会确认修改</li>
                <li>也可以使用 + 和 - 按钮调整数量</li>
            </ul>
        </div>

        <div id="cart-items">
            <!-- 购物车商品将在这里显示 -->
        </div>
    </div>

    <script>
        // 模拟购物车数据
        const cartItems = [
            {
                productId: '1',
                name: '儿童家具',
                price: 11.00,
                quantity: 12,
                image: '📦'
            },
            {
                productId: '2',
                name: '家具',
                price: 1.00,
                quantity: 1,
                image: '✈️'
            },
            {
                productId: '3',
                name: '电子产品',
                price: 25.00,
                quantity: 3,
                image: '📱'
            }
        ];

        // 购物车管理器
        class CartManager {
            constructor() {
                this.cart = cartItems;
                this.renderCart();
            }

            renderCart() {
                const container = document.getElementById('cart-items');
                container.innerHTML = this.cart.map(item => `
                    <div class="cart-item">
                        <div class="product-image">${item.image}</div>
                        <div class="product-info">
                            <div class="product-name">${item.name}</div>
                            <div class="product-price">¥ ${item.price.toFixed(2)}</div>
                        </div>
                        <div class="quantity-controls">
                            <button class="cart-quantity-btn minus" onclick="cartManager.updateQuantity('${item.productId}', ${item.quantity - 1})" ${item.quantity <= 1 ? 'disabled' : ''}>−</button>
                            <span class="quantity-display" onclick="cartManager.editQuantity('${item.productId}', ${item.quantity}, this)">${item.quantity}</span>
                            <button class="cart-quantity-btn plus" onclick="cartManager.updateQuantity('${item.productId}', ${item.quantity + 1})" ${item.quantity >= 99 ? 'disabled' : ''}>+</button>
                        </div>
                    </div>
                `).join('');
            }

            updateQuantity(productId, newQuantity) {
                if (newQuantity <= 0) {
                    return;
                }

                if (newQuantity > 99) {
                    alert('单个商品在购物车中的数量不能超过99件');
                    return;
                }

                const item = this.cart.find(item => item.productId === productId);
                if (item) {
                    item.quantity = newQuantity;
                    this.renderCart();
                    console.log(`商品 ${item.name} 数量更新为: ${newQuantity}`);
                }
            }

            editQuantity(productId, currentQuantity, element) {
                // 创建输入框
                const input = document.createElement('input');
                input.type = 'number';
                input.min = '1';
                input.max = '99';
                input.value = currentQuantity;
                input.className = 'quantity-input-field';
                
                // 替换显示元素
                const parent = element.parentNode;
                parent.replaceChild(input, element);
                
                // 聚焦并选中文本
                input.focus();
                input.select();
                
                // 处理输入完成
                const handleComplete = () => {
                    let newQuantity = parseInt(input.value) || 1;

                    // 确保数量在有效范围内
                    if (newQuantity < 1) {
                        newQuantity = 1;
                    } else if (newQuantity > 99) {
                        newQuantity = 99;
                        alert('单个商品在购物车中的数量不能超过99件');
                    }
                    
                    // 恢复显示元素
                    const span = document.createElement('span');
                    span.className = 'quantity-display';
                    span.onclick = () => this.editQuantity(productId, newQuantity, span);
                    span.textContent = newQuantity;
                    
                    parent.replaceChild(span, input);
                    
                    // 更新数量
                    if (newQuantity !== currentQuantity) {
                        this.updateQuantity(productId, newQuantity);
                    }
                };
                
                // 绑定事件
                input.addEventListener('blur', handleComplete);
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        handleComplete();
                    } else if (e.key === 'Escape') {
                        // 取消编辑，恢复原值
                        const span = document.createElement('span');
                        span.className = 'quantity-display';
                        span.onclick = () => this.editQuantity(productId, currentQuantity, span);
                        span.textContent = currentQuantity;
                        parent.replaceChild(span, input);
                    }
                });
            }
        }

        // 初始化购物车管理器
        const cartManager = new CartManager();
    </script>
</body>
</html>
