if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const API_CONFIG = {
    baseUrl: "http://localhost:8080",
    timeout: 1e4
  };
  function callAPI(endpoint, data = {}, method = "POST") {
    return new Promise((resolve, reject) => {
      const url = `${API_CONFIG.baseUrl}/api/${endpoint}`;
      const requestOptions = {
        url,
        method,
        timeout: API_CONFIG.timeout,
        header: {
          "Content-Type": "application/json"
        }
      };
      if (method === "POST") {
        requestOptions.data = data;
      } else if (method === "GET" && Object.keys(data).length > 0) {
        const params = new URLSearchParams(data).toString();
        requestOptions.url = `${url}?${params}`;
      }
      uni.request({
        ...requestOptions,
        success: (response) => {
          if (response.statusCode === 200) {
            resolve(response.data);
          } else {
            reject({
              success: false,
              message: `请求失败，状态码：${response.statusCode}`,
              statusCode: response.statusCode
            });
          }
        },
        fail: (error) => {
          formatAppLog("error", "at utils/api.js:48", "API请求失败:", error);
          reject({
            success: false,
            message: "网络连接失败，请检查网络设置",
            error
          });
        }
      });
    });
  }
  function loginAPI(username, password) {
    return callAPI("login", { username, password });
  }
  function getCurrentUser() {
    try {
      const userData = uni.getStorageSync("loggedInUser");
      if (userData && userData.isLoggedIn) {
        return userData;
      }
      return null;
    } catch (error) {
      formatAppLog("error", "at utils/user.js:15", "获取用户信息失败:", error);
      return null;
    }
  }
  function saveUserInfo(userData) {
    try {
      const userInfo = {
        ...userData,
        isLoggedIn: true,
        loginTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      uni.setStorageSync("loggedInUser", userInfo);
      return true;
    } catch (error) {
      formatAppLog("error", "at utils/user.js:34", "保存用户信息失败:", error);
      return false;
    }
  }
  function clearUserInfo() {
    try {
      uni.removeStorageSync("loggedInUser");
      return true;
    } catch (error) {
      formatAppLog("error", "at utils/user.js:47", "清除用户信息失败:", error);
      return false;
    }
  }
  function isUserLoggedIn() {
    const user = getCurrentUser();
    return user !== null && user.isLoggedIn === true;
  }
  function getUserDisplayName(user) {
    if (!user)
      return "未知用户";
    if (user.nickname)
      return user.nickname;
    if (user.username)
      return user.username;
    if (user.email)
      return user.email.split("@")[0];
    return "用户";
  }
  const userStateListeners = [];
  function triggerUserStateChange(event, data) {
    userStateListeners.forEach((callback) => {
      try {
        callback(event, data);
      } catch (error) {
        formatAppLog("error", "at utils/user.js:194", "用户状态监听器执行失败:", error);
      }
    });
  }
  const _imports_0 = "/static/logo.jpg";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$3 = {
    data() {
      return {
        currentTab: "account",
        isLoading: false,
        logoClickCount: 0,
        errorMessage: "",
        loginForm: {
          username: "",
          password: ""
        }
      };
    },
    methods: {
      // 切换登录方式
      switchTab(tab) {
        this.currentTab = tab;
        this.errorMessage = "";
      },
      // Logo点击事件（隐藏功能：点击5次跳转客服登录）
      onLogoClick() {
        this.logoClickCount++;
        if (this.logoClickCount === 5) {
          this.logoClickCount = 0;
          uni.showToast({
            title: "客服登录功能",
            icon: "none"
          });
        }
      },
      // 处理登录
      async handleLogin() {
        if (!this.loginForm.username.trim()) {
          this.showError("请输入用户名或邮箱");
          return;
        }
        if (!this.loginForm.password.trim()) {
          this.showError("请输入密码");
          return;
        }
        this.isLoading = true;
        this.errorMessage = "";
        try {
          const result = await loginAPI(
            this.loginForm.username.trim(),
            this.loginForm.password.trim()
          );
          if (result.success) {
            const userData = {
              username: result.user.username,
              email: result.user.email || "",
              id: result.user.id,
              nickname: result.user.nickname || "",
              avatar: result.user.avatar || "",
              roles: result.user.roles || [],
              permissions: result.user.permissions || []
            };
            if (saveUserInfo(userData)) {
              triggerUserStateChange("login", userData);
              uni.showToast({
                title: "登录成功",
                icon: "success"
              });
              setTimeout(() => {
                uni.reLaunch({
                  url: "/pages/index/index"
                });
              }, 1500);
            } else {
              this.showError("保存用户信息失败");
            }
          } else {
            if (result.blacklisted) {
              this.showError(result.message || "账号已被限制登录");
            } else {
              this.showError(result.message || "用户名或密码错误");
            }
          }
        } catch (error) {
          formatAppLog("error", "at pages/login/login.vue:188", "登录失败:", error);
          if (error.message) {
            this.showError(error.message);
          } else {
            this.showError("登录过程中出现错误，请稍后重试");
          }
        } finally {
          this.isLoading = false;
        }
      },
      // 显示错误信息
      showError(message) {
        this.errorMessage = message;
        setTimeout(() => {
          this.errorMessage = "";
        }, 3e3);
      },
      // 忘记密码
      forgotPassword() {
        uni.showToast({
          title: "忘记密码功能开发中",
          icon: "none"
        });
      },
      // 跳转注册页面
      goToRegister() {
        uni.showToast({
          title: "注册功能开发中",
          icon: "none"
        });
      }
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "login-container" }, [
      vue.createCommentVNode(" 头部logo区域 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("image", {
          class: "logo",
          src: _imports_0,
          onClick: _cache[0] || (_cache[0] = (...args) => $options.onLogoClick && $options.onLogoClick(...args))
        }),
        vue.createElementVNode("text", { class: "title" }, "用户登录")
      ]),
      vue.createCommentVNode(" 登录表单区域 "),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createCommentVNode(" 登录方式切换 "),
        vue.createElementVNode("view", { class: "login-tabs" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentTab === "account" }]),
              onClick: _cache[1] || (_cache[1] = ($event) => $options.switchTab("account"))
            },
            " 账号密码登录 ",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentTab === "wechat" }]),
              onClick: _cache[2] || (_cache[2] = ($event) => $options.switchTab("wechat"))
            },
            " 微信登录 ",
            2
            /* CLASS */
          )
        ]),
        vue.createCommentVNode(" 账号密码登录 "),
        $data.currentTab === "account" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "login-form"
        }, [
          vue.createElementVNode("view", { class: "form-group" }, [
            vue.createElementVNode("text", { class: "label" }, "用户名/邮箱"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "input",
                type: "text",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.loginForm.username = $event),
                placeholder: "请输入用户名或邮箱",
                maxlength: "320"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.loginForm.username]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-group" }, [
            vue.createElementVNode("text", { class: "label" }, "密码"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "input",
                type: "password",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.loginForm.password = $event),
                placeholder: "请输入密码",
                maxlength: "64"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.loginForm.password]
            ]),
            vue.createElementVNode("view", { class: "forgot-password" }, [
              vue.createElementVNode("text", {
                onClick: _cache[5] || (_cache[5] = (...args) => $options.forgotPassword && $options.forgotPassword(...args))
              }, "忘记密码？")
            ])
          ]),
          vue.createElementVNode("button", {
            class: "login-btn",
            disabled: $data.isLoading,
            onClick: _cache[6] || (_cache[6] = (...args) => $options.handleLogin && $options.handleLogin(...args))
          }, vue.toDisplayString($data.isLoading ? "登录中..." : "登录"), 9, ["disabled"])
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 微信登录 "),
        $data.currentTab === "wechat" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "wechat-login"
        }, [
          vue.createElementVNode("view", { class: "wechat-qrcode" }, [
            vue.createElementVNode("text", { class: "wechat-icon" }, "📱")
          ]),
          vue.createElementVNode("text", { class: "wechat-text" }, "请使用微信扫描二维码登录"),
          vue.createElementVNode("text", { class: "wechat-text" }, '或在微信中搜索"金舟国际物流"小程序')
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 注册链接 "),
        vue.createElementVNode("view", { class: "register-link" }, [
          vue.createElementVNode("text", null, "还没有账号？"),
          vue.createElementVNode("text", {
            class: "link-text",
            onClick: _cache[7] || (_cache[7] = (...args) => $options.goToRegister && $options.goToRegister(...args))
          }, "立即注册")
        ])
      ]),
      vue.createCommentVNode(" 错误提示 "),
      $data.errorMessage ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "error-container"
      }, [
        vue.createElementVNode(
          "text",
          { class: "error-text" },
          vue.toDisplayString($data.errorMessage),
          1
          /* TEXT */
        )
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__scopeId", "data-v-e4e4508d"], ["__file", "F:/abc/bbbbb/15/jinzhou/uni-app-Vue3/pages/login/login.vue"]]);
  const _sfc_main$2 = {
    data() {
      return {
        userInfo: null
      };
    },
    computed: {
      userDisplayName() {
        return getUserDisplayName(this.userInfo);
      }
    },
    onLoad() {
      this.checkUserLogin();
    },
    onShow() {
      this.checkUserLogin();
    },
    methods: {
      // 检查用户登录状态
      checkUserLogin() {
        this.userInfo = getCurrentUser();
      },
      // 跳转到登录页面
      goToLogin() {
        uni.navigateTo({
          url: "/pages/login/login"
        });
      },
      // 处理退出登录
      handleLogout() {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              if (clearUserInfo()) {
                triggerUserStateChange("logout", null);
                this.userInfo = null;
                uni.showToast({
                  title: "已退出登录",
                  icon: "success"
                });
                setTimeout(() => {
                  uni.reLaunch({
                    url: "/pages/login/login"
                  });
                }, 1500);
              } else {
                uni.showToast({
                  title: "退出登录失败",
                  icon: "error"
                });
              }
            }
          }
        });
      },
      // 跳转到指定页面
      goToPage(page) {
        if (!this.userInfo) {
          uni.showToast({
            title: "请先登录",
            icon: "none"
          });
          this.goToLogin();
          return;
        }
        uni.showToast({
          title: `${page}功能开发中`,
          icon: "none"
        });
      },
      // 新建订单
      createOrder() {
        if (!this.userInfo) {
          uni.showToast({
            title: "请先登录",
            icon: "none"
          });
          this.goToLogin();
          return;
        }
        uni.showToast({
          title: "新建订单功能开发中",
          icon: "none"
        });
      },
      // 查询物流
      queryTracking() {
        uni.showToast({
          title: "查询物流功能开发中",
          icon: "none"
        });
      },
      // 跳转到测试页面
      goToTest() {
        uni.navigateTo({
          url: "/pages/test/test"
        });
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" 头部区域 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("image", {
          class: "logo",
          src: _imports_0
        }),
        vue.createElementVNode("view", { class: "header-text" }, [
          vue.createElementVNode("text", { class: "company-name" }, "金舟国际物流"),
          $data.userInfo ? (vue.openBlock(), vue.createElementBlock(
            "text",
            {
              key: 0,
              class: "welcome-text"
            },
            "欢迎，" + vue.toDisplayString($options.userDisplayName),
            1
            /* TEXT */
          )) : (vue.openBlock(), vue.createElementBlock("text", {
            key: 1,
            class: "welcome-text"
          }, "欢迎使用"))
        ])
      ]),
      vue.createCommentVNode(" 功能菜单区域 "),
      vue.createElementVNode("view", { class: "menu-grid" }, [
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[0] || (_cache[0] = ($event) => $options.goToPage("order"))
        }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "📦"),
          vue.createElementVNode("text", { class: "menu-text" }, "订单管理")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[1] || (_cache[1] = ($event) => $options.goToPage("tracking"))
        }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "🚚"),
          vue.createElementVNode("text", { class: "menu-text" }, "物流跟踪")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[2] || (_cache[2] = ($event) => $options.goToPage("warehouse"))
        }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "🏪"),
          vue.createElementVNode("text", { class: "menu-text" }, "仓库管理")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[3] || (_cache[3] = ($event) => $options.goToPage("finance"))
        }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "💰"),
          vue.createElementVNode("text", { class: "menu-text" }, "财务管理")
        ])
      ]),
      vue.createCommentVNode(" 快捷操作区域 "),
      vue.createElementVNode("view", { class: "quick-actions" }, [
        vue.createElementVNode("text", { class: "section-title" }, "快捷操作"),
        vue.createElementVNode("view", { class: "action-buttons" }, [
          vue.createElementVNode("button", {
            class: "action-btn primary",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.createOrder && $options.createOrder(...args))
          }, "新建订单"),
          vue.createElementVNode("button", {
            class: "action-btn secondary",
            onClick: _cache[5] || (_cache[5] = (...args) => $options.queryTracking && $options.queryTracking(...args))
          }, "查询物流")
        ])
      ]),
      vue.createCommentVNode(" 用户操作区域 "),
      vue.createElementVNode("view", { class: "user-actions" }, [
        !$data.userInfo ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 0,
          class: "login-btn",
          onClick: _cache[6] || (_cache[6] = (...args) => $options.goToLogin && $options.goToLogin(...args))
        }, "登录")) : (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "user-info"
        }, [
          vue.createElementVNode("button", {
            class: "logout-btn",
            onClick: _cache[7] || (_cache[7] = (...args) => $options.handleLogout && $options.handleLogout(...args))
          }, "退出登录")
        ]))
      ]),
      vue.createCommentVNode(" 开发测试区域 "),
      vue.createElementVNode("view", { class: "dev-section" }, [
        vue.createElementVNode("button", {
          class: "test-btn",
          onClick: _cache[8] || (_cache[8] = (...args) => $options.goToTest && $options.goToTest(...args))
        }, "功能测试")
      ])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__scopeId", "data-v-1cf27b2a"], ["__file", "F:/abc/bbbbb/15/jinzhou/uni-app-Vue3/pages/index/index.vue"]]);
  const _sfc_main$1 = {
    data() {
      return {
        userInfo: null,
        apiResult: "",
        storageResult: "",
        systemInfo: {}
      };
    },
    computed: {
      userDisplayName() {
        return getUserDisplayName(this.userInfo);
      },
      isLoggedIn() {
        return isUserLoggedIn();
      },
      loginTime() {
        return this.userInfo ? new Date(this.userInfo.loginTime).toLocaleString() : null;
      }
    },
    onLoad() {
      this.loadUserInfo();
      this.getSystemInfo();
    },
    methods: {
      // 加载用户信息
      loadUserInfo() {
        this.userInfo = getCurrentUser();
      },
      // 获取系统信息
      getSystemInfo() {
        uni.getSystemInfo({
          success: (res) => {
            this.systemInfo = {
              "平台": res.platform,
              "系统": res.system,
              "版本": res.version,
              "屏幕宽度": res.screenWidth + "px",
              "屏幕高度": res.screenHeight + "px",
              "状态栏高度": res.statusBarHeight + "px"
            };
          }
        });
      },
      // 测试API连接
      async testAPI() {
        try {
          this.apiResult = "正在测试API连接...";
          const result = await callAPI("test", {}, "GET");
          if (result.success) {
            this.apiResult = "API连接成功！";
          } else {
            this.apiResult = `API连接失败：${result.message}`;
          }
        } catch (error) {
          this.apiResult = `API连接错误：${error.message || "未知错误"}`;
        }
      },
      // 测试登录API
      async testLogin() {
        try {
          this.apiResult = "正在测试登录API...";
          const result = await loginAPI("<EMAIL>", "test123");
          if (result.success) {
            this.apiResult = "登录API测试成功！";
          } else {
            this.apiResult = `登录API测试失败：${result.message}`;
          }
        } catch (error) {
          this.apiResult = `登录API测试错误：${error.message || "未知错误"}`;
        }
      },
      // 测试本地存储
      testStorage() {
        try {
          const testData = {
            test: true,
            timestamp: (/* @__PURE__ */ new Date()).toISOString(),
            data: "测试数据"
          };
          uni.setStorageSync("test_data", testData);
          const retrievedData = uni.getStorageSync("test_data");
          if (retrievedData && retrievedData.test) {
            this.storageResult = "本地存储测试成功！";
          } else {
            this.storageResult = "本地存储测试失败！";
          }
        } catch (error) {
          this.storageResult = `本地存储测试错误：${error.message}`;
        }
      },
      // 清除存储
      clearStorage() {
        try {
          uni.removeStorageSync("test_data");
          clearUserInfo();
          this.storageResult = "存储已清除！";
          this.loadUserInfo();
        } catch (error) {
          this.storageResult = `清除存储失败：${error.message}`;
        }
      },
      // 跳转到登录页
      goToLogin() {
        uni.navigateTo({
          url: "/pages/login/login"
        });
      },
      // 跳转到首页
      goToIndex() {
        uni.switchTab({
          url: "/pages/index/index"
        });
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "test-container" }, [
      vue.createElementVNode("view", { class: "test-header" }, [
        vue.createElementVNode("text", { class: "test-title" }, "功能测试页面")
      ]),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "用户状态测试"),
        vue.createElementVNode("view", { class: "test-item" }, [
          vue.createElementVNode("text", { class: "test-label" }, "当前用户："),
          vue.createElementVNode(
            "text",
            { class: "test-value" },
            vue.toDisplayString($data.userInfo ? $options.userDisplayName : "未登录"),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "test-item" }, [
          vue.createElementVNode("text", { class: "test-label" }, "登录状态："),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["test-value", { success: $options.isLoggedIn, error: !$options.isLoggedIn }])
            },
            vue.toDisplayString($options.isLoggedIn ? "已登录" : "未登录"),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "test-item" }, [
          vue.createElementVNode("text", { class: "test-label" }, "登录时间："),
          vue.createElementVNode(
            "text",
            { class: "test-value" },
            vue.toDisplayString($options.loginTime || "无"),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "API测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.testAPI && $options.testAPI(...args))
          }, "测试API连接"),
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[1] || (_cache[1] = (...args) => $options.testLogin && $options.testLogin(...args))
          }, "测试登录API")
        ]),
        $data.apiResult ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "api-result"
        }, [
          vue.createElementVNode("text", { class: "result-title" }, "API测试结果："),
          vue.createElementVNode(
            "text",
            { class: "result-content" },
            vue.toDisplayString($data.apiResult),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "存储测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[2] || (_cache[2] = (...args) => $options.testStorage && $options.testStorage(...args))
          }, "测试本地存储"),
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[3] || (_cache[3] = (...args) => $options.clearStorage && $options.clearStorage(...args))
          }, "清除存储")
        ]),
        $data.storageResult ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "storage-result"
        }, [
          vue.createElementVNode("text", { class: "result-title" }, "存储测试结果："),
          vue.createElementVNode(
            "text",
            { class: "result-content" },
            vue.toDisplayString($data.storageResult),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "页面跳转测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.goToLogin && $options.goToLogin(...args))
          }, "跳转登录页"),
          vue.createElementVNode("button", {
            class: "test-btn",
            onClick: _cache[5] || (_cache[5] = (...args) => $options.goToIndex && $options.goToIndex(...args))
          }, "跳转首页")
        ])
      ]),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "系统信息"),
        vue.createElementVNode("view", { class: "system-info" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.systemInfo, (value, key) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "info-item",
                key
              }, [
                vue.createElementVNode(
                  "text",
                  { class: "info-label" },
                  vue.toDisplayString(key) + "：",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "info-value" },
                  vue.toDisplayString(value),
                  1
                  /* TEXT */
                )
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])
    ]);
  }
  const PagesTestTest = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-727d09f0"], ["__file", "F:/abc/bbbbb/15/jinzhou/uni-app-Vue3/pages/test/test.vue"]]);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/test/test", PagesTestTest);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch - 金舟国际物流");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "F:/abc/bbbbb/15/jinzhou/uni-app-Vue3/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
