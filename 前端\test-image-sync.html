<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车图片同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #0c4da2;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .product-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .product-info {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
        }
        .product-info h4 {
            margin-top: 0;
            color: #0c4da2;
        }
        .product-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info-row {
            margin: 8px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
            word-break: break-all;
        }
        .difference {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .same {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0a3d82;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #0c4da2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav-links a:hover {
            background-color: #0a3d82;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">购物车图片同步测试</h1>
        
        <div class="nav-links">
            <a href="my-products.html" target="_blank">打开购物车页面</a>
            <a href="admin-dashboard.html" target="_blank">打开商品管理页面</a>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="testImageSync()">测试图片同步</button>
            <button onclick="compareAllProducts()">比较所有商品</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        // 测试图片同步功能
        async function testImageSync() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result info">正在测试购物车图片同步功能...</div>';

            try {
                // 检查是否有登录用户
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (!userDataStr) {
                    resultsDiv.innerHTML = '<div class="test-result error">错误：未找到登录用户信息，请先登录</div>';
                    return;
                }

                const userData = JSON.parse(userDataStr);
                resultsDiv.innerHTML += '<div class="test-result success">✓ 找到登录用户：' + userData.username + '</div>';

                // 获取购物车数据
                const cartResponse = await fetch(`/api/cart/${encodeURIComponent(userData.username)}`);
                const cartData = await cartResponse.json();

                if (!cartData.success || !cartData.cart || cartData.cart.length === 0) {
                    resultsDiv.innerHTML += '<div class="test-result info">购物车为空，请先添加商品进行测试</div>';
                    return;
                }

                resultsDiv.innerHTML += '<div class="test-result success">✓ 购物车中有 ' + cartData.cart.length + ' 个商品</div>';

                // 测试每个商品的图片同步
                for (let i = 0; i < cartData.cart.length; i++) {
                    const cartItem = cartData.cart[i];
                    await testSingleProductImageSync(cartItem, resultsDiv);
                }

            } catch (error) {
                resultsDiv.innerHTML += '<div class="test-result error">测试失败：' + error.message + '</div>';
            }
        }

        // 测试单个商品的图片同步
        async function testSingleProductImageSync(cartItem, resultsDiv) {
            try {
                // 获取商品最新信息
                const productResponse = await fetch(`/api/products/${cartItem.productId}`);
                const productData = await productResponse.json();

                if (!productData.success) {
                    resultsDiv.innerHTML += '<div class="test-result error">✗ 获取商品 ' + cartItem.productId + ' 信息失败</div>';
                    return;
                }

                const product = productData.product;

                // 获取商品的最新图片
                let latestImage = '';
                if (product.images && product.images.length > 0) {
                    latestImage = product.images[0].url;
                } else if (product.mainImage) {
                    latestImage = product.mainImage;
                }

                // 创建比较界面
                const comparisonHtml = `
                    <div class="product-comparison">
                        <div class="product-info">
                            <h4>购物车中的商品信息</h4>
                            <div class="info-row">
                                <span class="info-label">商品ID:</span>
                                <span class="info-value">${cartItem.productId}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">名称:</span>
                                <span class="info-value">${cartItem.name}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">价格:</span>
                                <span class="info-value">¥${cartItem.price}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">图片URL:</span>
                                <span class="info-value">${cartItem.image || '无'}</span>
                            </div>
                            ${cartItem.image ? `<img src="${cartItem.image}" alt="购物车图片" class="product-image" onerror="this.src='https://via.placeholder.com/100x100/f0f0f0/999999?text=加载失败'">` : '<div style="width:100px;height:100px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border:1px solid #ddd;">无图片</div>'}
                        </div>
                        <div class="product-info">
                            <h4>商品管理中的最新信息</h4>
                            <div class="info-row">
                                <span class="info-label">商品ID:</span>
                                <span class="info-value">${product.id}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">名称:</span>
                                <span class="info-value">${product.name}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">价格:</span>
                                <span class="info-value">¥${product.price}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">图片URL:</span>
                                <span class="info-value">${latestImage || '无'}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Images数组:</span>
                                <span class="info-value">${JSON.stringify(product.images || [])}</span>
                            </div>
                            ${latestImage ? `<img src="${latestImage}" alt="最新图片" class="product-image" onerror="this.src='https://via.placeholder.com/100x100/f0f0f0/999999?text=加载失败'">` : '<div style="width:100px;height:100px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border:1px solid #ddd;">无图片</div>'}
                        </div>
                    </div>
                `;

                // 检查是否需要同步
                const needsSync = cartItem.image !== latestImage;
                const syncStatus = needsSync ? 
                    '<div class="test-result difference">⚠️ 图片需要同步更新</div>' : 
                    '<div class="test-result same">✓ 图片已同步</div>';

                resultsDiv.innerHTML += `
                    <div style="border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 8px;">
                        <h3>商品: ${product.name} (${cartItem.productId})</h3>
                        ${syncStatus}
                        ${comparisonHtml}
                    </div>
                `;

            } catch (error) {
                resultsDiv.innerHTML += '<div class="test-result error">测试商品 ' + cartItem.productId + ' 失败：' + error.message + '</div>';
            }
        }

        // 比较所有商品
        async function compareAllProducts() {
            await testImageSync();
        }

        // 清除结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // 页面加载时显示说明
        window.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `
                <div class="test-result info">
                    <h4>购物车图片同步测试说明：</h4>
                    <ul>
                        <li><strong>测试目的：</strong>验证购物车中的商品图片是否与商品管理中的最新图片保持同步</li>
                        <li><strong>测试步骤：</strong>
                            <ol>
                                <li>确保已登录用户账户</li>
                                <li>在购物车中添加一些商品</li>
                                <li>在商品管理页面修改商品的主图片</li>
                                <li>点击"测试图片同步"按钮查看同步状态</li>
                                <li>返回购物车页面验证图片是否自动更新</li>
                            </ol>
                        </li>
                        <li><strong>修复内容：</strong>
                            <ul>
                                <li>✅ 修复了购物车同步时图片字段映射错误的问题</li>
                                <li>✅ 正确从商品的images[0].url获取主图片</li>
                                <li>✅ 添加了图片获取失败时的占位图片处理</li>
                                <li>✅ 增加了调试日志以便跟踪图片同步过程</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            `;
        });
    </script>
</body>
</html>
