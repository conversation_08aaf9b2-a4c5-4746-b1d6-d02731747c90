<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存检查功能演示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .cart-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .item-details {
            font-size: 14px;
            color: #666;
        }

        .stock-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .stock-sufficient {
            background-color: #d4edda;
            color: #155724;
        }

        .stock-insufficient {
            background-color: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #1e7e34;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
        }

        /* 库存不足弹窗样式 */
        .stock-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .stock-modal {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .stock-modal-header {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            background-color: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .stock-modal-header i {
            color: #ff6b6b;
            font-size: 24px;
            margin-right: 10px;
        }

        .stock-modal-header h3 {
            flex: 1;
            margin: 0;
            color: #333;
            font-size: 18px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        .stock-modal-body {
            padding: 20px;
        }

        .insufficient-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 10px;
            background-color: #fafafa;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }

        .modal-quantity-btn {
            background-color: #007bff;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .modal-quantity-btn:hover {
            background-color: #0056b3;
        }

        .quantity-input {
            width: 60px;
            height: 30px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 5px;
            font-size: 14px;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s ease;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .stock-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #eee;
            background-color: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shopping-cart"></i> 库存检查功能演示</h1>
            <p>演示购物车中的库存检查和数量调整功能</p>
        </div>
        
        <div class="content">
            <div class="demo-section">
                <h2><i class="fas fa-box"></i> 模拟购物车商品</h2>
                <div class="cart-item">
                    <div class="item-info">
                        <div class="item-name">红木家具</div>
                        <div class="item-details">价格: ¥11 | 购买数量: 3</div>
                    </div>
                    <div class="stock-status stock-insufficient">库存不足 (库存: 1)</div>
                </div>
                
                <div class="cart-item">
                    <div class="item-info">
                        <div class="item-name">商品1</div>
                        <div class="item-details">价格: ¥1 | 购买数量: 2</div>
                    </div>
                    <div class="stock-status stock-insufficient">库存不足 (库存: 0)</div>
                </div>
            </div>
            
            <div class="demo-section">
                <h2><i class="fas fa-cog"></i> 功能测试</h2>
                <button class="btn btn-success" onclick="testSingleItemPurchase()">
                    <i class="fas fa-shopping-bag"></i> 测试单个商品立即购买
                </button>
                <button class="btn btn-warning" onclick="testCheckoutAll()">
                    <i class="fas fa-credit-card"></i> 测试结算全部商品
                </button>
            </div>
            
            <div class="demo-section">
                <h2><i class="fas fa-info-circle"></i> 功能说明</h2>
                <ul>
                    <li><strong>单个商品立即购买：</strong>只检查该商品的库存，如果数量超过库存则提示并修改数量</li>
                    <li><strong>结算全部商品：</strong>检查所有商品的库存，如果任何商品数量超过库存则提示并修改所有相关商品数量</li>
                    <li><strong>库存不足处理：</strong>弹出调整窗口，用户可以修改数量或删除商品</li>
                    <li><strong>自动调整：</strong>系统会自动将数量调整为可用库存数量</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟商品数据
        const mockCartItems = [
            {
                productId: "P1753528795392123",
                name: "红木家具",
                price: 11,
                quantity: 3,
                currentStock: 1,
                requestedQuantity: 3
            },
            {
                productId: "P1753678517992803",
                name: "商品1",
                price: 1,
                quantity: 2,
                currentStock: 0,
                requestedQuantity: 2
            }
        ];

        // 测试单个商品立即购买
        function testSingleItemPurchase() {
            const item = mockCartItems[0]; // 红木家具
            if (item.quantity > item.currentStock) {
                showStockInsufficientModal([item], 'single');
            } else {
                alert('库存充足，可以立即购买！');
            }
        }

        // 测试结算全部商品
        function testCheckoutAll() {
            const insufficientItems = mockCartItems.filter(item => item.quantity > item.currentStock);
            if (insufficientItems.length > 0) {
                showStockInsufficientModal(insufficientItems, 'cart');
            } else {
                alert('所有商品库存充足，可以结算！');
            }
        }

        // 显示库存不足弹窗
        function showStockInsufficientModal(insufficientItems, type) {
            const modalHtml = `
                <div class="stock-modal-overlay" id="stock-modal-overlay">
                    <div class="stock-modal">
                        <div class="stock-modal-header">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>库存不足</h3>
                            <button class="close-btn" onclick="closeStockModal()">&times;</button>
                        </div>
                        <div class="stock-modal-body">
                            <p>以下商品库存不足，请调整数量：</p>
                            <div class="insufficient-items">
                                ${insufficientItems.map(item => `
                                    <div class="insufficient-item">
                                        <div class="item-info">
                                            <span class="item-name">${item.name}</span>
                                            <div class="stock-info">
                                                当前库存: ${item.currentStock} | 购买数量: ${item.requestedQuantity}
                                            </div>
                                        </div>
                                        <div class="quantity-controls">
                                            <button class="modal-quantity-btn" onclick="adjustModalQuantityByStep('${item.productId}', -1)">-</button>
                                            <input type="number" class="quantity-input" value="${item.currentStock}" min="0" max="${item.currentStock}"
                                                   onchange="adjustModalQuantity('${item.productId}', parseInt(this.value))"
                                                   data-product-id="${item.productId}">
                                            <button class="modal-quantity-btn" onclick="adjustModalQuantityByStep('${item.productId}', 1)">+</button>
                                        </div>
                                        <button class="delete-btn" onclick="adjustModalQuantity('${item.productId}', 0)">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stock-modal-footer">
                            <button class="btn btn-secondary" onclick="closeStockModal()">取消</button>
                            <button class="btn btn-primary" onclick="confirmStockAdjustment('${type}')">确认调整</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // 关闭库存不足弹窗
        function closeStockModal() {
            const modal = document.getElementById('stock-modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // 调整弹窗中的商品数量
        function adjustModalQuantity(productId, newQuantity) {
            const input = document.querySelector(`input[data-product-id="${productId}"]`);
            if (input) {
                const maxQuantity = parseInt(input.getAttribute('max'));

                if (newQuantity < 0) newQuantity = 0;
                if (newQuantity > maxQuantity) newQuantity = maxQuantity;

                input.value = newQuantity;
            }
        }

        // 通过步长调整弹窗中的商品数量（用于+/-按钮）
        function adjustModalQuantityByStep(productId, step) {
            const input = document.querySelector(`input[data-product-id="${productId}"]`);
            if (input) {
                const currentQuantity = parseInt(input.value) || 0;
                const newQuantity = currentQuantity + step;
                adjustModalQuantity(productId, newQuantity);
            }
        }

        // 确认库存调整
        function confirmStockAdjustment(type) {
            const inputs = document.querySelectorAll('.stock-modal .quantity-input');
            const adjustments = [];
            
            inputs.forEach(input => {
                const productId = input.getAttribute('data-product-id');
                const newQuantity = parseInt(input.value) || 0;
                adjustments.push({ productId, newQuantity });
            });

            closeStockModal();

            if (type === 'single') {
                alert(`单个商品购买：已调整商品数量\n${adjustments.map(adj => `商品ID: ${adj.productId}, 新数量: ${adj.newQuantity}`).join('\n')}\n\n现在可以进入支付页面！`);
            } else if (type === 'cart') {
                alert(`购物车结算：已调整所有商品数量\n${adjustments.map(adj => `商品ID: ${adj.productId}, 新数量: ${adj.newQuantity}`).join('\n')}\n\n现在可以进入支付页面！`);
            }
        }
    </script>
</body>
</html>
