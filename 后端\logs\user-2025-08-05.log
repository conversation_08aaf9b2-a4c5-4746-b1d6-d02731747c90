[2025-08-05T10:47:59.865Z] 用户登录成功: 1111
[2025-08-05T10:48:01.273Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-05T10:48:01.274Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\15\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-05T10:48:01.275Z] 购物车API - 购物车文件存在，正在读取
[2025-08-05T10:48:01.276Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-05T10:48:01.277Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-05T10:48:01.292Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-05T10:48:03.790Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-05T10:48:03.791Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\15\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-05T10:48:03.795Z] 购物车API - 购物车文件存在，正在读取
[2025-08-05T10:48:03.796Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-05T10:48:03.796Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-05T10:48:03.807Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-05T10:48:04.570Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-05T10:48:04.571Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\15\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-05T10:48:04.571Z] 购物车API - 购物车文件存在，正在读取
[2025-08-05T10:48:04.572Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-05T10:48:04.572Z] 购物车API - 返回购物车数据，商品数量: 2
