
.container[data-v-1cf27b2a] {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 1.25rem;
}
.header[data-v-1cf27b2a] {
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 0.625rem;
	padding: 1.25rem;
	margin-bottom: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.logo[data-v-1cf27b2a] {
	width: 3.75rem;
	height: 3.75rem;
	border-radius: 0.625rem;
	margin-right: 0.9375rem;
}
.header-text[data-v-1cf27b2a] {
	flex: 1;
}
.company-name[data-v-1cf27b2a] {
	display: block;
	font-size: 1.5rem;
	font-weight: bold;
	color: #0c4da2;
	margin-bottom: 0.3125rem;
}
.welcome-text[data-v-1cf27b2a] {
	font-size: 0.875rem;
	color: #666;
}
.menu-grid[data-v-1cf27b2a] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.9375rem;
	margin-bottom: 1.25rem;
}
.menu-item[data-v-1cf27b2a] {
	background-color: #fff;
	border-radius: 0.625rem;
	padding: 1.25rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}
.menu-item[data-v-1cf27b2a]:active {
	transform: scale(0.95);
}
.menu-icon[data-v-1cf27b2a] {
	font-size: 2.5rem;
	margin-bottom: 0.625rem;
}
.menu-text[data-v-1cf27b2a] {
	display: block;
	font-size: 0.875rem;
	color: #333;
	font-weight: bold;
}
.quick-actions[data-v-1cf27b2a] {
	background-color: #fff;
	border-radius: 0.625rem;
	padding: 1.25rem;
	margin-bottom: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-1cf27b2a] {
	display: block;
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.9375rem;
}
.action-buttons[data-v-1cf27b2a] {
	display: flex;
	gap: 0.625rem;
}
.action-btn[data-v-1cf27b2a] {
	flex: 1;
	padding: 0.75rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	font-weight: bold;
	border: none;
}
.action-btn.primary[data-v-1cf27b2a] {
	background-color: #0c4da2;
	color: white;
}
.action-btn.secondary[data-v-1cf27b2a] {
	background-color: #f8f9fa;
	color: #0c4da2;
	border: 0.0625rem solid #0c4da2;
}
.user-actions[data-v-1cf27b2a] {
	text-align: center;
}
.login-btn[data-v-1cf27b2a] {
	background-color: #0c4da2;
	color: white;
	border: none;
	padding: 0.75rem 1.875rem;
	border-radius: 0.375rem;
	font-size: 1rem;
	font-weight: bold;
}
.user-info[data-v-1cf27b2a] {
	display: flex;
	justify-content: center;
}
.logout-btn[data-v-1cf27b2a] {
	background-color: #dc3545;
	color: white;
	border: none;
	padding: 0.625rem 1.25rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
}
.dev-section[data-v-1cf27b2a] {
	margin-top: 1.25rem;
	text-align: center;
}
.test-btn[data-v-1cf27b2a] {
	background-color: #17a2b8;
	color: white;
	border: none;
	padding: 0.625rem 1.25rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
}
