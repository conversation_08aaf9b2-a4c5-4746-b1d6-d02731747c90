// API配置和请求工具类
const API_CONFIG = {
	baseUrl: 'http://localhost:8080',
	timeout: 10000
};

/**
 * 统一的API请求方法
 * @param {string} endpoint - API端点
 * @param {object} data - 请求数据
 * @param {string} method - 请求方法，默认POST
 * @returns {Promise} 请求结果
 */
export function callAPI(endpoint, data = {}, method = 'POST') {
	return new Promise((resolve, reject) => {
		const url = `${API_CONFIG.baseUrl}/api/${endpoint}`;
		
		const requestOptions = {
			url: url,
			method: method,
			timeout: API_CONFIG.timeout,
			header: {
				'Content-Type': 'application/json'
			}
		};
		
		if (method === 'POST') {
			requestOptions.data = data;
		} else if (method === 'GET' && Object.keys(data).length > 0) {
			const params = new URLSearchParams(data).toString();
			requestOptions.url = `${url}?${params}`;
		}
		
		uni.request({
			...requestOptions,
			success: (response) => {
				if (response.statusCode === 200) {
					resolve(response.data);
				} else {
					reject({
						success: false,
						message: `请求失败，状态码：${response.statusCode}`,
						statusCode: response.statusCode
					});
				}
			},
			fail: (error) => {
				console.error('API请求失败:', error);
				reject({
					success: false,
					message: '网络连接失败，请检查网络设置',
					error: error
				});
			}
		});
	});
}

/**
 * 用户登录API
 * @param {string} username - 用户名或邮箱
 * @param {string} password - 密码
 * @returns {Promise} 登录结果
 */
export function loginAPI(username, password) {
	return callAPI('login', { username, password });
}

/**
 * 用户注册API
 * @param {object} userData - 用户注册数据
 * @returns {Promise} 注册结果
 */
export function registerAPI(userData) {
	return callAPI('register', userData);
}

/**
 * 获取用户信息API
 * @param {string} userId - 用户ID
 * @returns {Promise} 用户信息
 */
export function getUserInfoAPI(userId) {
	return callAPI(`user/${userId}`, {}, 'GET');
}

/**
 * 修改密码API
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 修改结果
 */
export function changePasswordAPI(oldPassword, newPassword) {
	return callAPI('change-password', { oldPassword, newPassword });
}

/**
 * 忘记密码API
 * @param {string} email - 邮箱地址
 * @returns {Promise} 重置结果
 */
export function forgotPasswordAPI(email) {
	return callAPI('forgot-password', { email });
}

/**
 * 验证token有效性
 * @param {string} token - 用户token
 * @returns {Promise} 验证结果
 */
export function validateTokenAPI(token) {
	return callAPI('validate-token', { token });
}

// 导出API配置，供其他模块使用
export { API_CONFIG };
