
.test-container[data-v-727d09f0] {
	padding: 1.25rem;
	background-color: #f8f9fa;
	min-height: 100vh;
}
.test-header[data-v-727d09f0] {
	text-align: center;
	margin-bottom: 1.25rem;
}
.test-title[data-v-727d09f0] {
	font-size: 1.5rem;
	font-weight: bold;
	color: #0c4da2;
}
.test-section[data-v-727d09f0] {
	background-color: #fff;
	border-radius: 0.625rem;
	padding: 0.9375rem;
	margin-bottom: 0.9375rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-727d09f0] {
	display: block;
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.625rem;
	border-bottom: 0.0625rem solid #eee;
	padding-bottom: 0.3125rem;
}
.test-item[data-v-727d09f0] {
	display: flex;
	margin-bottom: 0.46875rem;
}
.test-label[data-v-727d09f0] {
	font-weight: bold;
	color: #666;
	width: 6.25rem;
}
.test-value[data-v-727d09f0] {
	flex: 1;
	color: #333;
}
.test-value.success[data-v-727d09f0] {
	color: #28a745;
}
.test-value.error[data-v-727d09f0] {
	color: #dc3545;
}
.test-buttons[data-v-727d09f0] {
	display: flex;
	gap: 0.625rem;
	margin-bottom: 0.625rem;
	flex-wrap: wrap;
}
.test-btn[data-v-727d09f0] {
	background-color: #0c4da2;
	color: white;
	border: none;
	padding: 0.625rem 0.9375rem;
	border-radius: 0.3125rem;
	font-size: 0.875rem;
	flex: 1;
	min-width: 6.25rem;
}
.api-result[data-v-727d09f0],
.storage-result[data-v-727d09f0] {
	background-color: #f8f9fa;
	border-radius: 0.3125rem;
	padding: 0.625rem;
	border-left: 0.125rem solid #0c4da2;
}
.result-title[data-v-727d09f0] {
	display: block;
	font-weight: bold;
	color: #0c4da2;
	margin-bottom: 0.3125rem;
}
.result-content[data-v-727d09f0] {
	color: #333;
	word-break: break-all;
}
.system-info[data-v-727d09f0] {
	background-color: #f8f9fa;
	border-radius: 0.3125rem;
	padding: 0.625rem;
}
.info-item[data-v-727d09f0] {
	display: flex;
	margin-bottom: 0.3125rem;
}
.info-label[data-v-727d09f0] {
	font-weight: bold;
	color: #666;
	width: 6.25rem;
}
.info-value[data-v-727d09f0] {
	flex: 1;
	color: #333;
}
