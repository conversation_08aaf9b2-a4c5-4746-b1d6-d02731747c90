[2025-08-03T06:52:57.629Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:52:57.631Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:52:57.631Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:52:57.633Z] 购物车API - 读取到的购物车数据，商品数量: 7
[2025-08-03T06:52:57.633Z] 购物车API - 返回购物车数据，商品数量: 7
[2025-08-03T06:53:05.165Z] 保存用户购物车: 1111, 商品数量: 6
[2025-08-03T06:53:05.664Z] 保存用户购物车: 1111, 商品数量: 5
[2025-08-03T06:53:06.184Z] 保存用户购物车: 1111, 商品数量: 4
[2025-08-03T06:53:08.801Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T06:53:09.041Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T06:53:09.248Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T06:53:09.480Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T06:53:10.795Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:53:10.796Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:53:10.797Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:53:10.798Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T06:53:10.799Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T06:53:13.841Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:53:13.842Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:53:13.842Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:53:13.843Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T06:53:13.844Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T06:54:18.785Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T06:54:19.818Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:19.820Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:19.821Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:19.821Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:19.822Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:20.440Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:20.441Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:20.442Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:20.443Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:20.443Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:25.497Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:25.499Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:25.500Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:25.501Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T06:54:25.502Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T06:54:28.026Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T06:54:28.863Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:28.865Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:28.865Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:28.866Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T06:54:28.867Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T06:54:29.805Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T06:54:29.806Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T06:54:29.807Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T06:54:29.807Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T06:54:29.808Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:22.649Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:22.651Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:22.652Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:22.652Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:22.653Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:26.010Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:26.011Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:26.011Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:26.012Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:26.012Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:30.393Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:01:30.872Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:01:46.132Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:46.133Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:46.134Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:46.134Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:46.135Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:51.097Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:51.098Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:51.099Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:51.100Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:51.100Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:54.776Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:54.778Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:54.779Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:54.779Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:54.780Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:55.567Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:55.569Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:55.569Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:55.570Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:55.571Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:01:57.936Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:01:59.045Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:01:59.046Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:01:59.047Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:01:59.048Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:01:59.048Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:02:09.550Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:02:09.552Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:02:09.552Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:02:09.553Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:02:09.553Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:07:49.129Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:07:49.130Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:07:49.131Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:07:49.132Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:07:49.133Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:04.356Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:04.357Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:04.359Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:04.360Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:04.361Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:06.168Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:06.169Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:06.170Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:06.170Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:06.171Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:16.627Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:16.628Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:16.629Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:16.630Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:16.631Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:21.147Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:21.149Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:21.150Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:21.151Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:21.152Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:23.984Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:23.986Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:23.987Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:23.989Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:23.990Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:26.531Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:26.532Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:26.533Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:26.535Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:26.535Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:28.199Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:28.201Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:28.201Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:28.203Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:28.203Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:08:34.751Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:08:34.753Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:08:34.753Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:08:34.754Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:08:34.754Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:40:11.647Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:11.649Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:11.650Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:11.651Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:40:11.651Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:40:18.564Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:18.565Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:18.566Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:18.567Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:40:18.568Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:40:31.460Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:31.461Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:31.462Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:31.463Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:40:31.463Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:40:40.722Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:40:40.950Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:40:47.510Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:40:52.368Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:40:52.401Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:52.402Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:52.403Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:52.405Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T07:40:52.405Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T07:40:55.643Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:55.644Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:55.645Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:55.646Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T07:40:55.647Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T07:40:57.909Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T07:40:58.366Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T07:40:58.693Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T07:40:58.982Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T07:40:59.989Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:40:59.990Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:40:59.990Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:40:59.991Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:40:59.992Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:00.801Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:00.802Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:00.803Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:00.804Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:00.804Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:05.951Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:41:06.833Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:06.834Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:06.835Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:06.836Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T07:41:06.837Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T07:41:11.063Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803
[2025-08-03T07:41:11.322Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803
[2025-08-03T07:41:12.723Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:12.724Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:12.725Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:12.726Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:12.726Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:14.590Z] 添加商品到购物车: 1111, 商品ID: P1753678456567209
[2025-08-03T07:41:14.817Z] 添加商品到购物车: 1111, 商品ID: P1753678456567209
[2025-08-03T07:41:16.000Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:16.001Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:16.002Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:16.002Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T07:41:16.003Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T07:41:16.861Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:16.862Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:16.862Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:16.863Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T07:41:16.863Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T07:41:19.997Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:41:22.225Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:22.226Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:22.226Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:22.227Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:22.227Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:25.035Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:25.036Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:25.036Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:25.037Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:25.037Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:27.566Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:41:30.828Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:41:30.853Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:30.854Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:30.854Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:30.855Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:30.855Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:41:58.876Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:41:58.877Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:41:58.877Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:41:58.878Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:41:58.878Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:42:01.109Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:42:01.356Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:49.846Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:50.063Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:51.790Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:52.007Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:52.246Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:52.502Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:52.774Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:53.021Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:53.253Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:53.518Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:53.782Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:43:54.037Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:49:56.714Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:49:56.715Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:49:56.716Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:49:56.716Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:49:56.717Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:50:07.495Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:07.526Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:50:07.527Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:50:07.528Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:50:07.529Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:50:07.529Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:50:15.637Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:15.845Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:16.069Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:31.951Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:31.959Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:50:31.998Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:50:32.000Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:50:32.000Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:50:32.001Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:50:32.002Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:51:45.911Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:51:46.127Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:51:46.430Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:52:00.368Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:52:00.640Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:52:01.879Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:52:02.119Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:02.901Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:03.287Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:03.520Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:03.743Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:04.896Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T07:52:14.160Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T07:52:14.375Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T07:52:14.599Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T07:52:24.559Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:52:24.560Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:52:24.561Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:52:24.561Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:52:24.562Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:52:28.471Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T07:52:29.809Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:52:29.810Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:52:29.811Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:52:29.812Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T07:52:29.813Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T07:52:30.489Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:52:30.490Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:52:30.491Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:52:30.492Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T07:52:30.492Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T07:52:38.207Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:11.731Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:58:11.732Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:58:11.732Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:58:11.733Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:58:11.733Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:58:28.894Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:29.311Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:29.567Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:29.798Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:30.039Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:30.263Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:33.464Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:33.663Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:33.880Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:34.088Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:34.304Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:34.518Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:34.759Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:34.984Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:35.217Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:35.447Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:35.663Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:35.904Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:36.135Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:58:36.480Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:59:27.847Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:59:27.883Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T07:59:27.884Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T07:59:27.885Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T07:59:27.886Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T07:59:27.886Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T07:59:53.235Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T07:59:54.904Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:15.735Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:02:15.737Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:02:15.737Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:02:15.738Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:02:15.739Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:02:17.537Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:18.223Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:18.551Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:27.055Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:27.855Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:28.079Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:28.346Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:28.615Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:29.016Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:29.520Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:32.982Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:02:32.983Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:02:32.984Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:02:32.984Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:02:32.985Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:02:35.544Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:35.752Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:35.968Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:36.199Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:02:36.449Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:03.757Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:03.758Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:03.759Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:03.759Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:03.760Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:05.568Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:06.273Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:07.034Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:07.320Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:07.800Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:08.057Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:08.281Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:15.959Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:15.960Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:15.961Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:15.962Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:15.962Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:18.118Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:18.119Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:18.120Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:18.120Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:18.121Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:21.193Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:21.419Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:21.640Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:21.872Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:22.113Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:22.361Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:22.617Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:27.145Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:27.402Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:27.651Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:27.914Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:28.177Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:28.425Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:28.771Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:42.060Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:42.061Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:42.061Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:42.062Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:42.062Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:45.129Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:45.328Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:45.535Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:07:52.068Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:52.069Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:52.070Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:52.071Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:52.072Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:53.323Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:53.324Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:53.325Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:53.325Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:53.325Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:07:56.600Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:07:56.602Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:07:56.603Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:07:56.604Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:07:56.606Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:05.609Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:08:08.987Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:08.988Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:08.989Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:08.990Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:08.990Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:10.337Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:10.339Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:10.339Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:10.340Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:10.341Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:12.400Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:12.402Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:12.403Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:12.404Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:12.404Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:22.387Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:22.389Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:22.389Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:22.390Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:22.390Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:28.857Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:30.090Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:30.336Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:31.457Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:31.688Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:31.888Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:32.144Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:32.392Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:32.639Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:41.066Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:41.076Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:41.110Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:41.110Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:41.111Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:41.111Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:41.111Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:08:45.372Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:45.705Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:45.920Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:46.721Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:46.952Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:49.713Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:49.722Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:08:49.761Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:08:49.762Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:08:49.762Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:08:49.763Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:08:49.763Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:09:00.338Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:09:00.553Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:09:00.792Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:09:01.065Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:09:08.505Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:09:08.538Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:09:08.538Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:09:08.539Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:09:08.539Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:09:08.540Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:09:11.394Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:09:11.395Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:09:11.396Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:09:11.396Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:09:11.396Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:10:00.231Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:10:00.233Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:10:00.233Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:10:00.234Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:10:00.235Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:10:30.346Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:10:30.348Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:10:30.349Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:10:30.350Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:10:30.350Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:10:37.337Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:15:29.291Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:15:29.293Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:15:29.294Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:15:29.294Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:15:29.295Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:15:48.708Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:15:55.134Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T08:16:31.357Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:16:31.358Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:16:31.359Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:16:31.360Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:16:31.361Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:16:33.182Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:16:33.184Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:16:33.184Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:16:33.185Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:16:33.185Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:16:34.825Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:16:34.826Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:16:34.827Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:16:34.828Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:16:34.828Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:18:14.955Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:18:50.103Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:18:50.104Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:18:50.105Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:18:50.106Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:18:50.106Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:18:51.175Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:18:51.176Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:18:51.177Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:18:51.178Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:18:51.179Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:18:52.551Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:18:52.552Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:18:52.553Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:18:52.554Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:18:52.554Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:22:18.444Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:22:18.445Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:22:18.446Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:22:18.447Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:22:18.447Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:22:31.703Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:22:31.704Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:22:31.705Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:22:31.706Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:22:31.707Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:22:33.278Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:22:33.279Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:22:33.280Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:22:33.280Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:22:33.281Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:22:52.544Z] 用户登录成功: 1111
[2025-08-03T08:22:53.811Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:22:53.812Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:22:53.813Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:22:53.814Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:22:53.814Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:23:05.571Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T08:23:05.615Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:23:05.617Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:23:05.617Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:23:05.618Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:23:05.619Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:23:08.655Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:23:08.657Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:23:08.658Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:23:08.659Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:23:08.659Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:23:56.970Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:23:56.971Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:23:56.972Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:23:56.973Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:23:56.973Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:24:00.581Z] 添加商品到购物车: 1111, 商品ID: P1753528795392123
[2025-08-03T08:24:03.301Z] 添加商品到购物车: 1111, 商品ID: P1753528795392123
[2025-08-03T08:24:05.101Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:05.102Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:05.103Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:05.104Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:05.104Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:06.233Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:06.233Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:06.234Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:06.234Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:06.235Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:13.012Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:13.013Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:13.014Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:13.015Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:13.015Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:14.964Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:14.965Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:14.967Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:14.967Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:14.968Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:18.680Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:18.682Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:18.683Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:18.684Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:18.685Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:27.522Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:27.523Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:27.524Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:27.524Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:27.524Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:32.396Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:24:32.436Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:32.437Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:32.437Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:32.438Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:32.438Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:24:35.165Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:24:35.167Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:24:35.169Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:24:35.170Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:24:35.171Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:27:33.342Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:27:33.343Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:27:33.344Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:27:33.345Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:27:33.345Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:28:35.289Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:28:35.291Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:28:35.292Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:28:35.292Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:28:35.293Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:33:01.589Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:33:05.661Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:33:05.664Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:33:05.666Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:33:05.667Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:33:05.667Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:33:08.993Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:33:08.994Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:33:08.995Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:33:08.995Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:33:08.996Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:33:13.070Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:33:13.072Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:33:13.072Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:33:13.073Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:33:13.073Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:33:17.725Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803
[2025-08-03T08:33:18.132Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803
[2025-08-03T08:33:18.386Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803
[2025-08-03T08:33:19.956Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:33:19.957Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:33:19.957Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:33:19.958Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:33:19.959Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:33:25.532Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:33:25.762Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:33:25.963Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:33:26.203Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:33:26.395Z] 添加商品到购物车: 1111, 商品ID: P175368263148042
[2025-08-03T08:33:28.196Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:33:28.197Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:33:28.197Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:33:28.198Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:33:28.199Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:34:19.583Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:34:19.584Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:34:19.585Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:34:19.585Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:34:19.585Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:34:25.597Z] 保存用户购物车: 1111, 商品数量: 4
[2025-08-03T08:34:25.610Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T08:34:25.620Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:34:25.694Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:34:25.695Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:34:25.696Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:34:25.696Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:34:25.697Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:34:29.627Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T08:34:34.684Z] 添加商品到购物车: 1111, 商品ID: P1753528795392123
[2025-08-03T08:34:36.525Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:34:36.526Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:34:36.526Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:34:36.527Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:34:36.528Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:38:15.495Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:15.496Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:15.496Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:15.497Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:38:15.497Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:38:16.913Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:16.914Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:16.915Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:16.916Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:38:16.917Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:38:18.248Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:18.249Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:18.249Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:18.250Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:38:18.251Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:38:20.820Z] 添加商品到购物车: 1111, 商品ID: P1753678541110123
[2025-08-03T08:38:21.050Z] 添加商品到购物车: 1111, 商品ID: P1753678541110123
[2025-08-03T08:38:21.269Z] 添加商品到购物车: 1111, 商品ID: P1753678541110123
[2025-08-03T08:38:22.335Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:22.337Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:22.338Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:22.339Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:22.339Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:28.025Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:28.025Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:28.026Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:28.026Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:28.027Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:42.558Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:42.560Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:42.561Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:42.561Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:42.562Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:43.798Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:43.800Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:43.801Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:43.801Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:43.802Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:44.998Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:44.999Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:45.000Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:45.001Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:45.001Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:54.214Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:54.216Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:54.217Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:54.218Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:54.218Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:38:57.845Z] 添加商品到购物车: 1111, 商品ID: P1753678541110123
[2025-08-03T08:38:59.952Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:38:59.954Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:38:59.955Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:38:59.956Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:38:59.957Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:39:01.166Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:39:01.167Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:39:01.168Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:39:01.168Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:39:01.169Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:39:04.855Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T08:39:04.893Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:39:04.894Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:39:04.894Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:39:04.895Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:39:04.896Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:39:09.014Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:39:09.015Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:39:09.016Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:39:09.017Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:39:09.018Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:03.154Z] 用户 jinzhousyxl888 上传图片: image-1754210403152-587809174.jpg, 物流码: 未指定
[2025-08-03T08:40:12.161Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:12.163Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:12.163Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:12.164Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:12.165Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:22.195Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:22.196Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:22.197Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:22.198Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:22.199Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:40.225Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:40.226Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:40.228Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:40.229Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:40.230Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:41.003Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:41.005Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:41.005Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:41.006Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:41.006Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:43.433Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:43.434Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:43.435Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:43.435Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:43.436Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:47.606Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:47.607Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:47.608Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:47.609Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:47.609Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:49.591Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:49.592Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:49.592Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:49.593Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:49.593Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:51.739Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:51.740Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:51.741Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:51.742Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:40:51.743Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:40:54.044Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:40:54.739Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:40:54.740Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:40:54.741Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:40:54.742Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:40:54.743Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:41:05.726Z] 用户 jinzhousyxl888 上传图片: image-1754210465725-457527199.png, 物流码: 未指定
[2025-08-03T08:41:16.559Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:41:16.559Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:41:16.560Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:41:16.560Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:41:16.561Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:41:18.440Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:41:18.441Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:41:18.442Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:41:18.443Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:41:18.443Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:41:24.832Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:41:24.833Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:41:24.834Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:41:24.835Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:41:24.835Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:41:54.138Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:41:54.140Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:41:54.141Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:41:54.141Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:41:54.142Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:03.293Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:42:03.294Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:42:03.295Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:42:03.296Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:42:03.296Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:14.059Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:42:14.061Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:42:14.062Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:42:14.063Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:42:14.064Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:21.998Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:42:21.999Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:42:22.000Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:42:22.000Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:42:22.001Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:24.190Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:42:24.191Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:42:24.191Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:42:24.192Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:42:24.193Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:35.771Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:42:35.772Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:42:35.773Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:42:35.774Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:42:35.775Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:42:51.153Z] 用户 jinzhousyxl888 上传图片: image-1754210571152-786600848.png, 物流码: 未指定
[2025-08-03T08:42:52.676Z] 用户 jinzhousyxl888 删除图片: image-1754210571152-786600848.png
[2025-08-03T08:42:56.639Z] 用户 jinzhousyxl888 上传图片: image-1754210576637-203521315.png, 物流码: 未指定
[2025-08-03T08:43:06.181Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:43:06.182Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:43:06.183Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:43:06.184Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:43:06.184Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:43:07.591Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:43:07.592Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:43:07.593Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:43:07.594Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:43:07.594Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:43:13.559Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:43:13.561Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:43:13.561Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:43:13.562Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:43:13.563Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:08.648Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:48:10.876Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:10.878Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:10.878Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:10.879Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:10.879Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:11.976Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:11.978Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:11.978Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:11.979Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:11.980Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:18.093Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T08:48:19.840Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:19.841Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:19.842Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:19.842Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T08:48:19.843Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T08:48:25.645Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:48:26.824Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:26.826Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:26.827Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:26.829Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:26.831Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:27.735Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:27.736Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:27.737Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:27.737Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:27.737Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:32.817Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:32.818Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:32.819Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:32.821Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:32.821Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:44.909Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:44.910Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:44.911Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:44.911Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:44.912Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:47.108Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:47.109Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:47.110Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:47.110Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:47.111Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:53.016Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:53.018Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:53.019Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:53.020Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:53.021Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:48:57.813Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:48:59.233Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:48:59.234Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:48:59.235Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:48:59.235Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:48:59.236Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:49:00.007Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:00.008Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:00.009Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:00.010Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:49:00.010Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:49:03.933Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:03.934Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:03.935Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:03.936Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:49:03.937Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:49:10.426Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:49:11.919Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:11.921Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:11.922Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:11.922Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:49:11.923Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:49:12.799Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:12.800Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:12.801Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:12.801Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T08:49:12.802Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T08:49:14.829Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T08:49:15.197Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:49:15.557Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T08:49:18.702Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T08:49:19.677Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:19.678Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:19.679Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:19.681Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:49:19.681Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:49:24.615Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:49:25.832Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:25.835Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:25.835Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:25.836Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:49:25.837Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:49:26.727Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:26.728Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:26.728Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:26.729Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:49:26.730Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:49:29.056Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:29.058Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:29.059Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:29.060Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:49:29.060Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:49:30.326Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:30.328Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:30.328Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:30.329Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:49:30.330Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:49:31.333Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T08:49:32.086Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:49:32.088Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:49:32.089Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:49:32.089Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:49:32.090Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:49:54.087Z] 添加商品到购物车: 1111, 商品ID: P17541284735762
[2025-08-03T08:50:02.331Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:50:02.333Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:50:02.333Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:50:02.334Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:50:02.335Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:50:03.111Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:50:03.112Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:50:03.113Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:50:03.114Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:50:03.114Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:11.779Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 5
[2025-08-03T08:55:19.936Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:19.937Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:19.938Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:19.938Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:19.939Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:41.117Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:41.119Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:41.120Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:41.120Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:41.121Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:44.130Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:44.131Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:44.132Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:44.132Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:44.133Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:44.726Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:44.727Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:44.728Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:44.729Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:44.729Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:46.227Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:46.228Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:46.229Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:46.230Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:46.231Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:50.934Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 4
[2025-08-03T08:55:52.237Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:52.240Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:52.241Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:52.247Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:52.247Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:55:53.155Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:55:53.156Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:55:53.156Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:55:53.157Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:55:53.157Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:56:27.309Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 3
[2025-08-03T08:56:31.543Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:56:31.544Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:56:31.545Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:56:31.546Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:56:31.546Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:57:29.293Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:57:29.294Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:57:29.295Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:57:29.296Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:57:29.297Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:57:30.146Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:57:30.148Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:57:30.148Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:57:30.149Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:57:30.150Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:57:34.320Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T08:57:35.898Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:57:35.900Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:57:35.900Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:57:35.901Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:57:35.902Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:08.274Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:08.275Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:08.276Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:08.277Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:08.278Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:09.289Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:09.290Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:09.291Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:09.292Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:09.292Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:38.562Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 5
[2025-08-03T08:58:40.021Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:40.023Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:40.023Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:40.025Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:40.025Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:40.719Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:40.720Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:40.721Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:40.722Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:40.722Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:45.193Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T08:58:45.242Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:45.243Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:45.244Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:45.244Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:58:45.245Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:58:46.473Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:46.474Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:46.474Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:46.475Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:58:46.475Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:58:49.707Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:49.708Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:49.709Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:49.711Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:58:49.711Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:58:55.824Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 111
[2025-08-03T08:58:57.154Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:57.155Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:57.156Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:57.157Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:57.157Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:58:57.760Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:58:57.761Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:58:57.762Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:58:57.763Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:58:57.763Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:59:08.800Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T08:59:08.824Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:08.825Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:08.826Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:08.827Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:59:08.827Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:59:10.816Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:10.817Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:10.817Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:10.818Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:59:10.819Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:59:14.139Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:14.140Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:14.141Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:14.142Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T08:59:14.142Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T08:59:25.080Z] 添加商品到购物车: 1111, 商品ID: P175368263148042, 数量: 111
[2025-08-03T08:59:26.812Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:26.813Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:26.814Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:26.815Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T08:59:26.816Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T08:59:38.874Z] 添加商品到购物车: 1111, 商品ID: P1753678541110123, 数量: 211
[2025-08-03T08:59:40.333Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:40.335Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:40.336Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:40.337Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:59:40.337Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:59:41.474Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:41.476Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:41.476Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:41.477Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:59:41.478Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:59:45.645Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:59:45.655Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T08:59:45.690Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:45.691Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:45.692Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:45.692Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:59:45.693Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T08:59:50.733Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T08:59:50.735Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T08:59:50.736Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T08:59:50.737Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T08:59:50.738Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:00:08.304Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 999
[2025-08-03T09:00:30.738Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:00:30.739Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:00:30.740Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:00:30.741Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:00:30.741Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:00:42.710Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:00:42.711Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:00:42.712Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:00:42.713Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:00:42.713Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:00:47.290Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:00:47.291Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:00:47.292Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:00:47.293Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:00:47.293Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:00:52.928Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:00:52.972Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:00:52.974Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:00:52.974Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:00:52.975Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:00:52.976Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:00:57.793Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:00:57.795Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:00:57.796Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:00:57.797Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:00:57.798Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:01:09.933Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:01:09.935Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:01:09.936Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:01:09.937Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:01:09.938Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:01:26.014Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:01:26.015Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:01:26.015Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:01:26.016Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:01:26.017Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:01:34.277Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:01:34.278Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:01:34.279Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:01:34.281Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:01:34.281Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:01:37.531Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:01:37.532Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:01:37.533Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:01:37.534Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:01:37.534Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:01:39.913Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:01:39.915Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:01:39.916Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:01:39.917Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:01:39.917Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:02:59.603Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:02:59.604Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:02:59.605Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:02:59.606Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:02:59.606Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:03:02.300Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:03:02.301Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:03:02.302Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:03:02.303Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:03:02.303Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:11:42.675Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:11:42.677Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:11:42.677Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:11:42.687Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:11:42.688Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:11:44.432Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:11:44.433Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:11:44.434Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:11:44.434Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:11:44.435Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:11:48.160Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:11:48.162Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:11:48.162Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:11:48.163Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:11:48.164Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:02.739Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:02.740Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:02.741Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:02.741Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:02.742Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:05.677Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:05.678Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:05.679Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:05.680Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:05.681Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:19.327Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:19.329Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:19.329Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:19.330Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:19.331Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:22.971Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:22.972Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:22.973Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:22.974Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:22.974Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:24.285Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:24.286Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:24.287Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:24.287Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:24.288Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:12:27.954Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:12:27.955Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:12:27.956Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:12:27.957Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:12:27.957Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:13:44.427Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:13:44.428Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:13:44.429Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:13:44.430Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:13:44.431Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:13:52.681Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:13:52.682Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:13:52.683Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:13:52.684Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:13:52.684Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:13:58.430Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:13:58.432Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:13:58.434Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:13:58.436Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:13:58.437Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:18:05.716Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:18:05.718Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:18:05.718Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:18:05.719Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:18:05.719Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:18:07.805Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:18:07.806Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:18:07.807Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:18:07.808Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:18:07.809Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:19:33.673Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:19:33.674Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:19:33.674Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:19:33.675Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:19:33.676Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:23:36.363Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:23:36.365Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:23:36.366Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:23:36.368Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:23:36.368Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:23:36.601Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:23:36.602Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:23:36.603Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:23:36.604Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:23:36.604Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:23:43.773Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:23:43.774Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:23:43.775Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:23:43.776Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:23:43.776Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:23:45.434Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:23:45.436Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:23:45.437Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:23:45.438Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:23:45.439Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:24:07.649Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:24:07.650Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:24:07.651Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:24:07.652Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:24:07.652Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:24:11.043Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:24:11.045Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:24:11.046Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:24:11.046Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:24:11.047Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:27:45.545Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:27:45.546Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:27:45.547Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:27:45.548Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:27:45.549Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:27:52.853Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 1111
[2025-08-03T09:27:56.759Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:27:56.760Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:27:56.761Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:27:56.763Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:27:56.763Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:27:57.606Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:27:57.607Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:27:57.608Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:27:57.608Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:27:57.609Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:28:00.738Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:28:00.739Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:28:00.740Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:28:00.741Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:28:00.742Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:28:06.252Z] 添加商品到购物车: 1111, 商品ID: P1753678456567209, 数量: 111
[2025-08-03T09:28:07.984Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:28:07.985Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:28:07.986Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:28:07.986Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:28:07.987Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:28:14.133Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:28:14.134Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:28:14.135Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:28:14.136Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:28:14.136Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:28:59.509Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:28:59.509Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:28:59.511Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:28:59.511Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:28:59.512Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:30:32.563Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 999
[2025-08-03T09:34:05.103Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:05.105Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:05.107Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:05.107Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:05.108Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:13.366Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:13.368Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:13.369Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:13.370Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:13.370Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:17.576Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:17.578Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:17.579Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:17.580Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:17.580Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:24.179Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 99
[2025-08-03T09:34:30.848Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 99
[2025-08-03T09:34:32.709Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:32.710Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:32.711Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:32.712Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:32.712Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:33.725Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:33.727Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:33.727Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:33.728Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:33.728Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:52.095Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:52.097Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:52.098Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:52.099Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:52.099Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:34:57.098Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:34:57.099Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:34:57.100Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:34:57.102Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:34:57.102Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:38:23.709Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:23.711Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:23.711Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:23.712Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:38:23.713Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:38:25.201Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:25.202Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:25.204Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:25.204Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:38:25.205Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:38:27.691Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T09:38:28.867Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:38:29.139Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:38:29.908Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T09:38:31.254Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:31.255Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:31.256Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:31.257Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T09:38:31.257Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T09:38:43.348Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 99
[2025-08-03T09:38:48.779Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:48.780Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:48.783Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:48.784Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:38:48.785Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:38:49.722Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:49.723Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:49.724Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:49.724Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:38:49.725Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:38:51.212Z] 保存用户购物车: 1111, 商品数量: 0
[2025-08-03T09:38:52.315Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:38:52.316Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:38:52.317Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:38:52.318Z] 购物车API - 读取到的购物车数据，商品数量: 0
[2025-08-03T09:38:52.319Z] 购物车API - 返回购物车数据，商品数量: 0
[2025-08-03T09:38:58.939Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 99
[2025-08-03T09:39:10.849Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:39:10.850Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:39:10.851Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:39:10.852Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:39:10.852Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:39:11.811Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:39:11.811Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:39:11.812Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:39:11.813Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:39:11.813Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:39:26.469Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:39:26.470Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:39:26.471Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:39:26.472Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:39:26.472Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:27.578Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:27.579Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:27.579Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:27.580Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:27.580Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:30.505Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:30.506Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:30.507Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:30.508Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:30.509Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:31.534Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:31.535Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:31.536Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:31.536Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:31.537Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:32.796Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:46:33.197Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:46:34.036Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:46:34.276Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:46:34.965Z] 保存用户购物车: 1111, 商品数量: 1
[2025-08-03T09:46:36.295Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:36.297Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:36.298Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:36.299Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:36.299Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:38.652Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:38.653Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:38.654Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:38.654Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:38.654Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:46:43.157Z] 添加商品到购物车: 1111, 商品ID: P17541284735762, 数量: 1
[2025-08-03T09:46:44.191Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:46:44.192Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:46:44.192Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:46:44.193Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:46:44.193Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:47:24.785Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:24.786Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:24.787Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:24.788Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:47:24.788Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:47:27.164Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:27.165Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:27.165Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:27.166Z] 购物车API - 读取到的购物车数据，商品数量: 1
[2025-08-03T09:47:27.166Z] 购物车API - 返回购物车数据，商品数量: 1
[2025-08-03T09:47:31.702Z] 添加商品到购物车: 1111, 商品ID: P175368263148042, 数量: 99
[2025-08-03T09:47:32.660Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:32.661Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:32.661Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:32.662Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:32.663Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:35.158Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:35.160Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:35.161Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:35.162Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:35.163Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:35.903Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:35.904Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:35.904Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:35.905Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:35.905Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:38.500Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:47:39.857Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:39.858Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:39.859Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:39.860Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:39.860Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:42.155Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:42.156Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:42.157Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:42.159Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:42.159Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:45.278Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:45.279Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:45.280Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:45.281Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:45.281Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:46.604Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:47:46.605Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:47:46.606Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:47:46.607Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:47:46.607Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:47:54.214Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:47:56.212Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:49:00.340Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:49:01.772Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:54:50.235Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:54:50.236Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:54:50.236Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:54:50.237Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:54:50.237Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:54:51.654Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:54:52.084Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:54:52.741Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:08.734Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:08.958Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:09.221Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:09.700Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:09.926Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:10.168Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T09:55:19.178Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:19.179Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:19.181Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:19.182Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:55:19.183Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:55:23.918Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:23.919Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:23.919Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:23.920Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T09:55:23.920Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T09:55:27.520Z] 添加商品到购物车: 1111, 商品ID: P1753678517992803, 数量: 99
[2025-08-03T09:55:28.181Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:28.181Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:28.182Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:28.183Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:55:28.183Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:55:31.421Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:31.423Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:31.424Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:31.425Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:55:31.425Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:55:32.912Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:32.913Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:32.914Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:32.914Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:55:32.915Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:55:34.508Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:55:34.509Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:55:34.509Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:55:34.510Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T09:55:34.510Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T09:55:36.996Z] 添加商品到购物车: 1111, 商品ID: P1753678456567209, 数量: 99
[2025-08-03T09:56:12.022Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:56:12.024Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:56:12.024Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:56:12.025Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:56:12.026Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:56:18.344Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:56:18.345Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:56:18.346Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:56:18.347Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:56:18.347Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:56:21.313Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:56:21.314Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:56:21.315Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:56:21.315Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:56:21.316Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:57:48.761Z] 用户 jinzhousyxl888 上传图片: image-1754215068758-798967610.png, 物流码: 未指定
[2025-08-03T09:58:02.610Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:58:02.611Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:58:02.611Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:58:02.612Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:58:02.612Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:58:35.485Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:58:35.487Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:58:35.487Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:58:35.488Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:58:35.489Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:59:33.117Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:59:33.119Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:59:33.119Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:59:33.120Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:59:33.120Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:59:35.784Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:59:35.785Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:59:35.786Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:59:35.787Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:59:35.788Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:59:38.810Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:59:38.811Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:59:38.811Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:59:38.812Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:59:38.812Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:59:50.450Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:59:50.452Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:59:50.453Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:59:50.454Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:59:50.454Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T09:59:51.472Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T09:59:51.473Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T09:59:51.474Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T09:59:51.475Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T09:59:51.475Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T10:01:03.533Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:01:03.534Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:01:03.534Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:01:03.535Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T10:01:03.536Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T10:01:06.651Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:01:06.652Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:01:06.653Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:01:06.653Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T10:01:06.653Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T10:01:11.476Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:01:11.477Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:01:11.478Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:01:11.478Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T10:01:11.479Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T10:09:56.187Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:09:56.188Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:09:56.188Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:09:56.189Z] 购物车API - 读取到的购物车数据，商品数量: 4
[2025-08-03T10:09:56.190Z] 购物车API - 返回购物车数据，商品数量: 4
[2025-08-03T10:09:56.208Z] 保存用户购物车: 1111, 商品数量: 4
[2025-08-03T10:09:58.108Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:09:58.119Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:10:16.243Z] 用户 jinzhousyxl888 上传图片: image-1754215816242-676607844.png, 物流码: 未指定
[2025-08-03T10:10:20.411Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:20.413Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:20.414Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:20.415Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:20.415Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:20.428Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:10:29.136Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:29.136Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:29.137Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:29.137Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:29.138Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:29.140Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:29.141Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:29.141Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:29.142Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:29.142Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:29.150Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:10:29.157Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:10:31.945Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:31.946Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:31.947Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:31.948Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:31.949Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:41.501Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:41.502Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:41.503Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:41.504Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:41.505Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:42.696Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:10:42.697Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:10:42.698Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:10:42.698Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:10:42.699Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:10:42.714Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:11:09.665Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:11:09.666Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:11:09.667Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:11:09.667Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:11:09.668Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:11:09.676Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:11:20.427Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:11:20.428Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:11:20.428Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:11:20.429Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:11:20.429Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:11:20.437Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:11:20.451Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:11:20.452Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:11:20.453Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:11:20.453Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:11:20.453Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:11:20.462Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:11:46.284Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:11:46.284Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:11:46.285Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:11:46.285Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:11:46.286Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:11:46.298Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:11:46.299Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:11:46.300Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:11:46.301Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:11:46.301Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:11:46.313Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:11:46.320Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:16:43.053Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:16:43.055Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:16:43.056Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:16:43.057Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:16:43.057Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:16:43.059Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:16:43.060Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:16:43.061Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:16:43.062Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:16:43.062Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:16:43.072Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:16:43.076Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:16:44.517Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:16:44.518Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:16:44.519Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:16:44.519Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:16:44.520Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:16:44.533Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:16:59.951Z] 用户 jinzhousyxl888 上传图片: image-1754216219950-731939179.png, 物流码: 未指定
[2025-08-03T10:17:07.083Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:07.084Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:07.085Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:07.086Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:17:07.086Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:17:07.088Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:07.089Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:07.090Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:07.091Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:17:07.091Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:17:07.100Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:17:07.110Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:17:09.132Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:09.134Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:09.135Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:09.136Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:17:09.136Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:17:23.685Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:23.687Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:23.688Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:23.689Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:17:23.690Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:17:24.526Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:24.527Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:24.528Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:24.529Z] 购物车API - 读取到的购物车数据，商品数量: 3
[2025-08-03T10:17:24.529Z] 购物车API - 返回购物车数据，商品数量: 3
[2025-08-03T10:17:24.544Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:17:39.376Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:17:39.391Z] 保存用户购物车: 1111, 商品数量: 3
[2025-08-03T10:17:39.396Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:39.409Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:39.415Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:39.426Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:39.475Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:39.476Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:39.477Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:39.478Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:39.479Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:41.345Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:46.334Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:46.336Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:46.336Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:46.337Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:46.337Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:46.340Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:46.341Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:46.342Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:46.343Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:46.344Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:46.351Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:46.354Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:49.816Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:49.817Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:49.818Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:49.818Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:49.819Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:49.820Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:49.821Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:49.822Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:49.823Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:49.823Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:49.832Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:49.835Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:55.685Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:55.686Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:55.686Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:55.687Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:55.687Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:55.690Z] 购物车API - 获取购物车请求，用户名: 1111
[2025-08-03T10:17:55.691Z] 购物车API - 购物车文件路径: F:\abc\bbbbb\16\jinzhou\后端\data\userdata\1111\cart.json
[2025-08-03T10:17:55.691Z] 购物车API - 购物车文件存在，正在读取
[2025-08-03T10:17:55.692Z] 购物车API - 读取到的购物车数据，商品数量: 2
[2025-08-03T10:17:55.692Z] 购物车API - 返回购物车数据，商品数量: 2
[2025-08-03T10:17:55.700Z] 保存用户购物车: 1111, 商品数量: 2
[2025-08-03T10:17:55.705Z] 保存用户购物车: 1111, 商品数量: 2
