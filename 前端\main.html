<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金舟国际物流 - 官方网站</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }
        
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
        }
        
        .logo {
            display: flex;
            flex-direction: row;
            align-items: center;
        }
        
        .logo-img {
            width: 50px;
            height: 50px;
            margin-right: 15px;
        }
        
        .logo-text {
            display: flex;
            flex-direction: column;
        }
        
        .logo h1 {
            color: #0c4da2;
            font-size: 28px;
            margin-bottom: 5px;
        }
        
        .gold {
            color: #D4AF37;
        }
        
        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .login-button {
            display: inline-block;
            background-color: #0c4da2;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .login-button:hover {
            background-color: #083778;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .login-button i {
            margin-right: 5px;
        }

        /* 金舟推荐好品按钮样式 */
        .recommend-button {
            display: inline-block;
            background-color: #D4AF37;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            border: none;
            cursor: pointer;
            white-space: nowrap;
        }

        .recommend-button:hover {
            background-color: #B8941F;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .social-icons {
            display: flex;
            gap: 20px;
        }
        
        .social-icons a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .social-icons a:hover {
            transform: translateY(-3px);
        }
        
        .social-icon-circle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-bottom: 5px;
        }
        
        .social-icon-text {
            font-size: 12px;
            color: #333;
            margin-top: 3px;
        }
        
        .wechat .social-icon-circle {
            background-color: #07C160;
            color: white;
        }
        
        .xiaohongshu .social-icon-circle {
            background-color: #FE2C55;
            color: white;
            font-weight: bold;
            font-style: normal;
            font-size: 18px;
        }
        
        .douyin .social-icon-circle {
            background: linear-gradient(to bottom right, #00f2ea, #ff0050);
            color: white;
        }
        
        .taobao .social-icon-circle {
            background-color: #FF4200;
            color: white;
            font-weight: bold;
            font-style: normal;
            font-size: 18px;
        }
        
        main {
            padding-top: 100px;
            padding-bottom: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        /* 手机端社交媒体区域 - 默认隐藏 */
        .mobile-social-section {
            display: none;
        }

        /* Card Styles */
        .cards-container {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-top: 30px;
            margin-bottom: 0;
            flex-wrap: nowrap;
        }
        
        .card {
            flex: 1;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
            padding: 60px 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .card:hover {
            transform: translateY(-12px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.18);
        }
        
        .card a {
            text-decoration: none;
            color: inherit;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
        
        .card-icon {
            font-size: 80px;
            margin-bottom: 40px;
            color: #0c4da2;
        }
        
        .card h3 {
            color: #0c4da2;
            font-size: 26px;
            margin-bottom: 30px;
        }
        
        .card p {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
            margin-top: 15px;
        }

        
        footer {
            background-color: transparent;
            color: #333;
            text-align: center;
            padding: 0;
            margin-top: -50px;
            position: relative;
            z-index: 1;
        }

        footer p {
            font-size: 14px;
        }
        
        /* User info styles */
        .user-info {
            position: relative;
            cursor: pointer;
        }
        
        .username-display {
            display: flex;
            align-items: center;
            background-color: #0c4da2;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .username-display:hover {
            background-color: #083778;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .username-display i {
            margin-right: 5px;
            font-size: 16px;
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 150px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            padding: 10px 0;
            margin-top: 5px;
            z-index: 100;
            transform: translateY(10px);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .user-dropdown.active {
            transform: translateY(0);
            opacity: 1;
            pointer-events: all;
        }
        
        .user-dropdown a {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.2s;
        }
        
        .user-dropdown a:hover {
            background-color: #f5f5f5;
            color: #0c4da2;
        }
        
        .user-dropdown a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 调整header布局 */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            position: relative;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        /* 金舟推荐好品按钮样式 */
        .recommend-header-button {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border: 2px solid #d4af37;
            border-radius: 25px;
            padding: 10px 20px;
            color: #b8860b;
            font-weight: bold;
            font-size: 16px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
            white-space: nowrap;
        }

        .recommend-header-button:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
            background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
            color: #8b6914;
        }

        .recommend-header-button i {
            margin-right: 8px;
            color: #d4af37;
        }
        
        /* 用户登录按钮和用户信息在移动端的样式调整 */
        @media (max-width: 768px) {
            .header-right {
                gap: 10px;
            }

            .social-icons {
                display: flex;
                gap: 15px;
                justify-content: center;
                margin-top: 10px;
                order: 4; /* 放在最后显示 */
            }

            .social-icons a {
                flex-direction: column;
                align-items: center;
            }

            .social-icon-circle {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            .social-icon-text {
                font-size: 10px;
                margin-top: 2px;
            }

            .username-display {
                padding: 6px 12px;
                font-size: 14px;
            }

            .recommend-header-button {
                font-size: 14px;
                padding: 8px 16px;
            }
        }

        @media (max-width: 480px) {
            .recommend-header-button {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        /* 手机端专用响应式设计 - 保持电脑端完美布局不变 */
        @media (max-width: 768px) {
            /* 整体背景优化 */
            body {
                background-color: #f8f9fa;
            }

            .container {
                padding: 0 12px;
            }
            /* 头部布局优化 */
            header .container {
                flex-direction: column;
                padding: 15px 20px;
                gap: 15px;
            }

            .logo {
                order: 1;
            }

            .logo h1 {
                font-size: 24px;
            }

            .logo p {
                font-size: 12px;
            }

            .logo-img {
                width: 40px;
                height: 40px;
                margin-right: 12px;
            }

            .recommend-header-button {
                position: static;
                transform: none;
                order: 2;
                font-size: 14px;
                padding: 8px 16px;
            }

            .header-right {
                order: 3;
                justify-content: center;
            }

            /* 主要内容区域调整 */
            main {
                padding-top: 140px; /* 增加顶部间距适应多行头部 */
                padding-bottom: 30px;
                background-color: #f8f9fa;
            }

            /* 页脚优化 */
            footer {
                background-color: #f8f9fa;
                padding: 20px 0;
                margin-top: 20px;
            }

            footer p {
                font-size: 12px;
                color: #666;
            }

            /* 手机端社交媒体区域 */
            .mobile-social-section {
                display: block;
                margin-top: 30px;
                padding: 25px 15px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 15px;
                text-align: center;
                border: 1px solid rgba(12, 77, 162, 0.1);
            }

            .mobile-social-title h3 {
                color: #0c4da2;
                font-size: 18px;
                margin-bottom: 5px;
                font-weight: 600;
            }

            .mobile-social-title p {
                color: #666;
                font-size: 12px;
                margin-bottom: 20px;
            }

            .mobile-social-icons {
                display: flex;
                justify-content: space-around;
                gap: 10px;
            }

            .mobile-social-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-decoration: none;
                transition: all 0.3s ease;
                padding: 10px 5px;
                border-radius: 10px;
            }

            .mobile-social-item:hover {
                transform: translateY(-3px);
                background-color: rgba(255, 255, 255, 0.8);
            }

            .mobile-social-circle {
                width: 45px;
                height: 45px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin-bottom: 8px;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            }

            .mobile-social-item.wechat .mobile-social-circle {
                background: linear-gradient(135deg, #1aad19 0%, #00d100 100%);
            }

            .mobile-social-item.xiaohongshu .mobile-social-circle {
                background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            }

            .mobile-social-item.douyin .mobile-social-circle {
                background: linear-gradient(135deg, #25f4ee 0%, #00d4ff 100%);
            }

            .mobile-social-item.taobao .mobile-social-circle {
                background: linear-gradient(135deg, #ff4000 0%, #ff6a00 100%);
            }

            .mobile-social-item span {
                font-size: 11px;
                color: #333;
                font-weight: 500;
            }

            /* 卡片容器手机端布局 - 精细优化 */
            .cards-container {
                flex-direction: column;
                gap: 15px;
                margin-top: 25px;
                padding: 0 5px; /* 增加左右内边距 */
            }

            .card {
                height: auto;
                padding: 25px 20px;
                min-height: 160px;
                border-radius: 12px; /* 更圆润的边角 */
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* 更柔和的阴影 */
                margin-bottom: 5px;
                background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
                border: 1px solid rgba(12, 77, 162, 0.05);
                position: relative;
                overflow: hidden;
            }

            .card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #0c4da2 0%, #D4AF37 100%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .card:hover::before {
                opacity: 1;
            }

            .card:hover {
                transform: translateY(-3px); /* 减少悬停效果 */
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            }

            .card-icon {
                font-size: 45px;
                margin-bottom: 15px;
                color: #0c4da2;
            }

            .card h3 {
                font-size: 18px;
                margin-bottom: 12px;
                font-weight: 600;
                line-height: 1.3;
            }

            .card p {
                font-size: 13px;
                line-height: 1.4;
                margin-top: 8px;
                color: #666;
                max-width: 280px;
                margin-left: auto;
                margin-right: auto;
            }
        }

        /* 小屏幕手机进一步优化 */
        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            header .container {
                padding: 12px 15px;
                gap: 12px;
            }

            .logo h1 {
                font-size: 20px;
            }

            .logo p {
                font-size: 10px;
            }

            .logo-img {
                width: 35px;
                height: 35px;
                margin-right: 10px;
            }

            main {
                padding-top: 125px;
            }

            .cards-container {
                gap: 12px;
                margin-top: 20px;
                padding: 0 3px;
            }

            .card {
                padding: 20px 15px;
                min-height: 150px;
                border-radius: 10px;
            }

            .card-icon {
                font-size: 40px;
                margin-bottom: 12px;
            }

            .card h3 {
                font-size: 16px;
                margin-bottom: 10px;
                line-height: 1.2;
            }

            .card p {
                font-size: 12px;
                line-height: 1.3;
                margin-top: 6px;
            }
        }

        /* 横屏手机优化 - 充分利用屏幕宽度 */
        @media (max-width: 768px) and (orientation: landscape) {
            header .container {
                flex-direction: row;
                padding: 8px 15px;
                gap: 10px;
                min-height: 60px;
            }

            .logo {
                order: 1;
                flex-shrink: 0;
            }

            .logo h1 {
                font-size: 18px;
                margin-bottom: 2px;
            }

            .logo p {
                font-size: 9px;
            }

            .logo-img {
                width: 30px;
                height: 30px;
                margin-right: 8px;
            }

            .recommend-header-button {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                order: 2;
                font-size: 11px;
                padding: 5px 10px;
            }

            .header-right {
                order: 3;
                flex-shrink: 0;
            }

            main {
                padding-top: 70px;
            }

            .cards-container {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: space-between;
                gap: 8px;
                margin-top: 12px;
                padding: 0 8px;
            }

            .card {
                flex: 0 1 calc(50% - 4px);
                height: 180px;
                padding: 12px 8px;
                min-height: auto;
                border-radius: 8px;
            }

            .card:hover {
                transform: translateY(-2px);
            }

            .card-icon {
                font-size: 32px;
                margin-bottom: 8px;
            }

            .card h3 {
                font-size: 13px;
                margin-bottom: 6px;
                line-height: 1.1;
                font-weight: 600;
            }

            .card p {
                font-size: 10px;
                line-height: 1.2;
                margin-top: 4px;
                color: #666;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <div class="logo-text">
                    <h1><span class="gold">金舟</span>国际物流</h1>
                    <p>Jin Zhou International Logistics</p>
                </div>
            </div>

            <a href="recommend.html" class="recommend-header-button">
                <i class="fas fa-star"></i>金舟推荐好品
            </a>

            <div class="header-right">
                <div class="social-icons">
                    <a href="wechat.html" class="wechat" title="微信">
                        <div class="social-icon-circle">
                            <i class="fab fa-weixin fa-lg"></i>
                        </div>
                        <span class="social-icon-text">微信</span>
                    </a>
                    <a href="xiaohongshu.html" class="xiaohongshu" title="小红书">
                        <div class="social-icon-circle">
                            <i>小</i>
                        </div>
                        <span class="social-icon-text">小红书</span>
                    </a>
                    <a href="douyin.html" class="douyin" title="抖音">
                        <div class="social-icon-circle">
                            <i class="fab fa-tiktok fa-lg"></i>
                        </div>
                        <span class="social-icon-text">抖音</span>
                    </a>
                    <a href="taobao.html" class="taobao" title="淘宝">
                        <div class="social-icon-circle">
                            <i>淘</i>
                        </div>
                        <span class="social-icon-text">淘宝</span>
                    </a>
                </div>

                <a href="login.html" class="login-button" id="loginButton">
                    <i class="fas fa-user"></i> 用户登录
                </a>
                
                <div class="user-info" id="userInfo" style="display: none;">
                    <span class="username-display"><i class="fas fa-user-circle"></i> <span id="usernameText"></span></span>
                    <div class="user-dropdown">
                        <a href="dashboard.html"><i class="fas fa-user"></i> 我的账户</a>
                        <a href="#" id="logoutButton"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <main>
        <div class="container">
            <div class="cards-container">
                <div class="card">
                    <a href="order.html">
                        <div class="card-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>如何下单</h3>
                        <p>了解我们简单便捷的下单流程</p>
                    </a>
                </div>
                
                <div class="card">
                    <a href="prices.html">
                        <div class="card-icon">
                            <i class="fas fa-ship"></i>
                        </div>
                        <h3>运输价格</h3>
                        <p>查询最新的运输价格和费用明细</p>
                    </a>
                </div>
                
                <div class="card">
                    <a href="warehouse.html">
                        <div class="card-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <h3>仓库动态</h3>
                        <p>获取最新的仓库运营和库存信息</p>
                    </a>
                </div>

                <div class="card">
                    <a href="advantages.html">
                        <div class="card-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <h3>我们的优势</h3>
                        <p>了解金舟国际物流的核心优势</p>
                    </a>
                </div>
                
                <div class="card">
                    <a href="app.html">
                        <div class="card-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>APP下载</h3>
                        <p>下载我们的手机应用，随时掌握物流动态</p>
                    </a>
                </div>
            </div>

            <!-- 手机端社交媒体区域 -->
            <div class="mobile-social-section">
                <div class="mobile-social-title">
                    <h3>关注我们</h3>
                    <p>获取最新物流资讯和优惠信息</p>
                </div>
                <div class="mobile-social-icons">
                    <a href="wechat.html" class="mobile-social-item wechat">
                        <div class="mobile-social-circle">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <span>微信</span>
                    </a>
                    <a href="xiaohongshu.html" class="mobile-social-item xiaohongshu">
                        <div class="mobile-social-circle">
                            <span>小</span>
                        </div>
                        <span>小红书</span>
                    </a>
                    <a href="douyin.html" class="mobile-social-item douyin">
                        <div class="mobile-social-circle">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <span>抖音</span>
                    </a>
                    <a href="taobao.html" class="mobile-social-item taobao">
                        <div class="mobile-social-circle">
                            <span>淘</span>
                        </div>
                        <span>淘宝</span>
                    </a>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>
    
    <script>
        // 检查用户登录状态
        document.addEventListener('DOMContentLoaded', function() {
            const loginButton = document.getElementById('loginButton');
            const userInfo = document.getElementById('userInfo');
            const usernameText = document.getElementById('usernameText');
            const logoutButton = document.getElementById('logoutButton');
            const userDropdown = document.querySelector('.user-dropdown');
            
            let dropdownTimeout;

            // 鼠标进入用户信息区域显示下拉菜单
            if (userInfo) {
                userInfo.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                    userDropdown.classList.add('active');
                });
                
                // 鼠标离开用户信息区域2秒后隐藏下拉菜单
                userInfo.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 250); // 0.25秒延迟
                });
                
                // 鼠标进入下拉菜单时清除隐藏计时器
                userDropdown.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                });
                
                // 鼠标离开下拉菜单2秒后隐藏
                userDropdown.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 500); // 0.5秒延迟
                });
            }

            // 安全地获取登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 获取用户数据，如果有的话
            const userData = JSON.parse(sessionStorage.getItem('loggedInUser') || '{}');
            
            // 如果用户已登录，定期检查其账号状态
            if (userData.isLoggedIn && userData.id) {
                // 使用黑名单处理模块检查账号状态
                BlacklistHandler.checkUserBlacklisted();
                setInterval(BlacklistHandler.checkUserBlacklisted, 30000); // 每30秒检查一次
            }

            // 更新UI状态
            function updateUserInterface() {
                if (loggedInUser && loggedInUser.isLoggedIn && loggedInUser.username) {
                    // 用户已登录，显示用户信息
                    if (loginButton) loginButton.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'block';
                    if (usernameText) usernameText.textContent = loggedInUser.username;
                } else {
                    // 用户未登录，显示登录按钮
                    if (loginButton) loginButton.style.display = 'block';
                    if (userInfo) userInfo.style.display = 'none';
                }
            }

            updateUserInterface();

            // 退出登录处理
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 清除登录信息
                    sessionStorage.removeItem('loggedInUser');
                    localStorage.removeItem('loggedInUser'); // 也清除localStorage中可能的数据

                    // 重新加载页面以更新状态
                    window.location.reload();
                });
            }
        });
    </script>
</body>
</html> 