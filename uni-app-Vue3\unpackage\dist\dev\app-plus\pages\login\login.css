
.login-container[data-v-e4e4508d] {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 1.875rem 1.25rem;
}
.header[data-v-e4e4508d] {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 2.5rem;
}
.logo[data-v-e4e4508d] {
	width: 3.75rem;
	height: 3.75rem;
	border-radius: 0.625rem;
	margin-bottom: 0.9375rem;
}
.title[data-v-e4e4508d] {
	font-size: 2rem;
	font-weight: bold;
	color: #0c4da2;
}
.form-container[data-v-e4e4508d] {
	background-color: #fff;
	border-radius: 0.625rem;
	padding: 1.875rem 1.25rem;
	box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);
}
.login-tabs[data-v-e4e4508d] {
	display: flex;
	margin-bottom: 1.5625rem;
	border-bottom: 0.0625rem solid #eee;
}
.tab-item[data-v-e4e4508d] {
	flex: 1;
	text-align: center;
	padding: 0.625rem 0;
	font-size: 1rem;
	font-weight: bold;
	color: #666;
	position: relative;
}
.tab-item.active[data-v-e4e4508d] {
	color: #0c4da2;
}
.tab-item.active[data-v-e4e4508d]::after {
	content: '';
	position: absolute;
	bottom: -0.0625rem;
	left: 0;
	width: 100%;
	height: 0.125rem;
	background-color: #0c4da2;
}
.login-form[data-v-e4e4508d] {
	display: flex;
	flex-direction: column;
}
.form-group[data-v-e4e4508d] {
	margin-bottom: 1.25rem;
}
.label[data-v-e4e4508d] {
	display: block;
	margin-bottom: 0.5rem;
	font-weight: bold;
	color: #0c4da2;
	font-size: 0.875rem;
}
.input[data-v-e4e4508d] {
	width: 100%;
	padding: 0.75rem;
	border: 0.0625rem solid #ddd;
	border-radius: 0.3125rem;
	font-size: 1rem;
	background-color: #fff;
}
.input[data-v-e4e4508d]:focus {
	border-color: #0c4da2;
}
.forgot-password[data-v-e4e4508d] {
	text-align: right;
	margin-top: 0.3125rem;
}
.forgot-password uni-text[data-v-e4e4508d] {
	color: #0c4da2;
	font-size: 0.875rem;
}
.login-btn[data-v-e4e4508d] {
	background-color: #0c4da2;
	color: white;
	border: none;
	padding: 0.75rem;
	border-radius: 0.3125rem;
	font-size: 1rem;
	font-weight: bold;
	margin-top: 0.625rem;
}
.login-btn[data-v-e4e4508d]:disabled {
	background-color: #ccc;
}
.wechat-login[data-v-e4e4508d] {
	text-align: center;
	padding: 1.25rem 0;
}
.wechat-qrcode[data-v-e4e4508d] {
	display: inline-block;
	background-color: #f5f5f5;
	padding: 1.875rem;
	border-radius: 0.625rem;
	margin-bottom: 1.25rem;
}
.wechat-icon[data-v-e4e4508d] {
	font-size: 6.25rem;
}
.wechat-text[data-v-e4e4508d] {
	display: block;
	color: #666;
	font-size: 0.875rem;
	margin-bottom: 0.625rem;
}
.register-link[data-v-e4e4508d] {
	text-align: center;
	margin-top: 1.25rem;
	padding-top: 1.25rem;
	border-top: 0.0625rem solid #eee;
	font-size: 0.875rem;
}
.link-text[data-v-e4e4508d] {
	color: #0c4da2;
	font-weight: bold;
	margin-left: 0.3125rem;
}
.error-container[data-v-e4e4508d] {
	background-color: #f8d7da;
	border: 0.0625rem solid #f5c6cb;
	border-radius: 0.3125rem;
	padding: 0.625rem;
	margin-top: 0.9375rem;
}
.error-text[data-v-e4e4508d] {
	color: #721c24;
	font-size: 0.875rem;
	text-align: center;
}
