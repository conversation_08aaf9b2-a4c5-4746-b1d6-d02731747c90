{"name": "jinzhou-logistics-app", "version": "1.0.0", "description": "金舟国际物流管理系统移动端应用", "main": "main.js", "scripts": {"dev:app": "uni-app", "dev:app-plus": "uni-app", "dev:custom": "uni-app", "dev:h5": "uni-app", "dev:mp-alipay": "uni-app", "dev:mp-baidu": "uni-app", "dev:mp-weixin": "uni-app", "dev:mp-toutiao": "uni-app", "dev:quickapp": "uni-app", "build:app": "uni-app build", "build:app-plus": "uni-app build", "build:custom": "uni-app build", "build:h5": "uni-app build", "build:mp-alipay": "uni-app build", "build:mp-baidu": "uni-app build", "build:mp-weixin": "uni-app build", "build:mp-toutiao": "uni-app build", "build:quickapp": "uni-app build"}, "keywords": ["uni-app", "vue3", "logistics", "mobile", "jinzhou"], "author": "金舟国际物流开发团队", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-alipay": "^3.0.0", "@dcloudio/uni-mp-baidu": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-mp-toutiao": "^3.0.0", "@dcloudio/uni-quickapp": "^3.0.0", "vue": "^3.2.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/uni-template-compiler": "^3.0.0", "@dcloudio/webpack-uni-mp-loader": "^3.0.0", "@dcloudio/webpack-uni-pages-loader": "^3.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "repository": {"type": "git", "url": "本地项目"}, "bugs": {"url": "联系开发团队"}, "homepage": "金舟国际物流"}