<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存检查测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>库存检查功能测试</h1>
    
    <div class="test-section">
        <h2>1. 登录测试用户</h2>
        <button class="btn-primary" onclick="loginTestUser()">登录用户 "111"</button>
        <div id="login-status" class="status"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 查看购物车</h2>
        <button class="btn-success" onclick="loadCart()">加载购物车</button>
        <div id="cart-status" class="status"></div>
        <div id="cart-items"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试库存检查</h2>
        <button class="btn-warning" onclick="testSingleItemStock()">测试单个商品库存检查</button>
        <button class="btn-warning" onclick="testAllItemsStock()">测试所有商品库存检查</button>
        <div id="stock-test-status" class="status"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 跳转到购物车页面</h2>
        <button class="btn-primary" onclick="goToCart()">打开购物车页面</button>
    </div>

    <script>
        // 登录测试用户
        function loginTestUser() {
            const userData = {
                id: "1750940439665",
                username: "111",
                email: "<EMAIL>"
            };
            
            sessionStorage.setItem('loggedInUser', JSON.stringify(userData));
            
            const statusDiv = document.getElementById('login-status');
            statusDiv.className = 'status success';
            statusDiv.textContent = '用户 "111" 登录成功！';
        }

        // 加载购物车
        async function loadCart() {
            try {
                const response = await fetch('/api/cart/111');
                const data = await response.json();
                
                const statusDiv = document.getElementById('cart-status');
                const itemsDiv = document.getElementById('cart-items');
                
                if (data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `购物车加载成功，共 ${data.cart.length} 件商品`;
                    
                    itemsDiv.innerHTML = '<h3>购物车商品：</h3>' + 
                        data.cart.map(item => `
                            <div style="border: 1px solid #ccc; padding: 10px; margin: 5px 0;">
                                <strong>${item.name}</strong><br>
                                价格: ¥${item.price}<br>
                                数量: ${item.quantity}<br>
                                库存: ${item.stock}<br>
                                商品ID: ${item.productId}
                            </div>
                        `).join('');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '购物车加载失败: ' + data.message;
                }
            } catch (error) {
                const statusDiv = document.getElementById('cart-status');
                statusDiv.className = 'status error';
                statusDiv.textContent = '购物车加载失败: ' + error.message;
            }
        }

        // 测试单个商品库存检查
        async function testSingleItemStock() {
            try {
                // 检查红木家具的库存（库存1，购买数量3，应该不足）
                const response = await fetch('/api/products/P1753528795392123');
                const data = await response.json();
                
                const statusDiv = document.getElementById('stock-test-status');
                
                if (data.success) {
                    const currentStock = data.product.stock;
                    const requestedQuantity = 3;
                    
                    statusDiv.className = currentStock >= requestedQuantity ? 'status success' : 'status error';
                    statusDiv.innerHTML = `
                        <strong>单个商品库存检查结果：</strong><br>
                        商品名称: ${data.product.name}<br>
                        当前库存: ${currentStock}<br>
                        请求数量: ${requestedQuantity}<br>
                        结果: ${currentStock >= requestedQuantity ? '库存充足' : '库存不足'}
                    `;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '获取商品信息失败';
                }
            } catch (error) {
                const statusDiv = document.getElementById('stock-test-status');
                statusDiv.className = 'status error';
                statusDiv.textContent = '库存检查失败: ' + error.message;
            }
        }

        // 测试所有商品库存检查
        async function testAllItemsStock() {
            try {
                const cartResponse = await fetch('/api/cart/111');
                const cartData = await cartResponse.json();
                
                if (!cartData.success) {
                    throw new Error('获取购物车失败');
                }
                
                const insufficientItems = [];
                
                for (const item of cartData.cart) {
                    const productResponse = await fetch(`/api/products/${item.productId}`);
                    const productData = await productResponse.json();
                    
                    if (productData.success) {
                        const currentStock = productData.product.stock || 0;
                        if (item.quantity > currentStock) {
                            insufficientItems.push({
                                ...item,
                                currentStock: currentStock
                            });
                        }
                    }
                }
                
                const statusDiv = document.getElementById('stock-test-status');
                
                if (insufficientItems.length === 0) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '所有商品库存充足！';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `
                        <strong>库存不足的商品：</strong><br>
                        ${insufficientItems.map(item => `
                            ${item.name}: 库存${item.currentStock}，需要${item.quantity}
                        `).join('<br>')}
                    `;
                }
            } catch (error) {
                const statusDiv = document.getElementById('stock-test-status');
                statusDiv.className = 'status error';
                statusDiv.textContent = '库存检查失败: ' + error.message;
            }
        }

        // 跳转到购物车页面
        function goToCart() {
            window.location.href = 'my-products.html';
        }
    </script>
</body>
</html>
