<template>
	<view class="test-container">
		<view class="test-header">
			<text class="test-title">功能测试页面</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">用户状态测试</text>
			<view class="test-item">
				<text class="test-label">当前用户：</text>
				<text class="test-value">{{ userInfo ? userDisplayName : '未登录' }}</text>
			</view>
			<view class="test-item">
				<text class="test-label">登录状态：</text>
				<text class="test-value" :class="{ success: isLoggedIn, error: !isLoggedIn }">
					{{ isLoggedIn ? '已登录' : '未登录' }}
				</text>
			</view>
			<view class="test-item">
				<text class="test-label">登录时间：</text>
				<text class="test-value">{{ loginTime || '无' }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">API测试</text>
			<view class="test-buttons">
				<button class="test-btn" @click="testAPI">测试API连接</button>
				<button class="test-btn" @click="testLogin">测试登录API</button>
			</view>
			<view v-if="apiResult" class="api-result">
				<text class="result-title">API测试结果：</text>
				<text class="result-content">{{ apiResult }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">存储测试</text>
			<view class="test-buttons">
				<button class="test-btn" @click="testStorage">测试本地存储</button>
				<button class="test-btn" @click="clearStorage">清除存储</button>
			</view>
			<view v-if="storageResult" class="storage-result">
				<text class="result-title">存储测试结果：</text>
				<text class="result-content">{{ storageResult }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">页面跳转测试</text>
			<view class="test-buttons">
				<button class="test-btn" @click="goToLogin">跳转登录页</button>
				<button class="test-btn" @click="goToIndex">跳转首页</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">系统信息</text>
			<view class="system-info">
				<view class="info-item" v-for="(value, key) in systemInfo" :key="key">
					<text class="info-label">{{ key }}：</text>
					<text class="info-value">{{ value }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getCurrentUser, getUserDisplayName, isUserLoggedIn, clearUserInfo } from '@/utils/user.js'
import { callAPI, loginAPI } from '@/utils/api.js'

export default {
	data() {
		return {
			userInfo: null,
			apiResult: '',
			storageResult: '',
			systemInfo: {}
		}
	},
	computed: {
		userDisplayName() {
			return getUserDisplayName(this.userInfo);
		},
		isLoggedIn() {
			return isUserLoggedIn();
		},
		loginTime() {
			return this.userInfo ? new Date(this.userInfo.loginTime).toLocaleString() : null;
		}
	},
	onLoad() {
		this.loadUserInfo();
		this.getSystemInfo();
	},
	methods: {
		// 加载用户信息
		loadUserInfo() {
			this.userInfo = getCurrentUser();
		},
		
		// 获取系统信息
		getSystemInfo() {
			uni.getSystemInfo({
				success: (res) => {
					this.systemInfo = {
						'平台': res.platform,
						'系统': res.system,
						'版本': res.version,
						'屏幕宽度': res.screenWidth + 'px',
						'屏幕高度': res.screenHeight + 'px',
						'状态栏高度': res.statusBarHeight + 'px'
					};
				}
			});
		},
		
		// 测试API连接
		async testAPI() {
			try {
				this.apiResult = '正在测试API连接...';
				
				// 测试一个简单的API调用
				const result = await callAPI('test', {}, 'GET');
				
				if (result.success) {
					this.apiResult = 'API连接成功！';
				} else {
					this.apiResult = `API连接失败：${result.message}`;
				}
			} catch (error) {
				this.apiResult = `API连接错误：${error.message || '未知错误'}`;
			}
		},
		
		// 测试登录API
		async testLogin() {
			try {
				this.apiResult = '正在测试登录API...';
				
				// 使用测试账号
				const result = await loginAPI('<EMAIL>', 'test123');
				
				if (result.success) {
					this.apiResult = '登录API测试成功！';
				} else {
					this.apiResult = `登录API测试失败：${result.message}`;
				}
			} catch (error) {
				this.apiResult = `登录API测试错误：${error.message || '未知错误'}`;
			}
		},
		
		// 测试本地存储
		testStorage() {
			try {
				const testData = {
					test: true,
					timestamp: new Date().toISOString(),
					data: '测试数据'
				};
				
				// 存储测试数据
				uni.setStorageSync('test_data', testData);
				
				// 读取测试数据
				const retrievedData = uni.getStorageSync('test_data');
				
				if (retrievedData && retrievedData.test) {
					this.storageResult = '本地存储测试成功！';
				} else {
					this.storageResult = '本地存储测试失败！';
				}
			} catch (error) {
				this.storageResult = `本地存储测试错误：${error.message}`;
			}
		},
		
		// 清除存储
		clearStorage() {
			try {
				uni.removeStorageSync('test_data');
				clearUserInfo();
				this.storageResult = '存储已清除！';
				this.loadUserInfo(); // 重新加载用户信息
			} catch (error) {
				this.storageResult = `清除存储失败：${error.message}`;
			}
		},
		
		// 跳转到登录页
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		
		// 跳转到首页
		goToIndex() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 40rpx;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.test-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #0c4da2;
}

.test-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	border-bottom: 2rpx solid #eee;
	padding-bottom: 10rpx;
}

.test-item {
	display: flex;
	margin-bottom: 15rpx;
}

.test-label {
	font-weight: bold;
	color: #666;
	width: 200rpx;
}

.test-value {
	flex: 1;
	color: #333;
}

.test-value.success {
	color: #28a745;
}

.test-value.error {
	color: #dc3545;
}

.test-buttons {
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.test-btn {
	background-color: #0c4da2;
	color: white;
	border: none;
	padding: 20rpx 30rpx;
	border-radius: 10rpx;
	font-size: 28rpx;
	flex: 1;
	min-width: 200rpx;
}

.api-result,
.storage-result {
	background-color: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
	border-left: 4rpx solid #0c4da2;
}

.result-title {
	display: block;
	font-weight: bold;
	color: #0c4da2;
	margin-bottom: 10rpx;
}

.result-content {
	color: #333;
	word-break: break-all;
}

.system-info {
	background-color: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
}

.info-item {
	display: flex;
	margin-bottom: 10rpx;
}

.info-label {
	font-weight: bold;
	color: #666;
	width: 200rpx;
}

.info-value {
	flex: 1;
	color: #333;
}
</style>
